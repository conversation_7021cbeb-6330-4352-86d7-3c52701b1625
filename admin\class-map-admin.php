<?php
/**
 * Admin functionality class
 *
 * @package MyAccessibilityPlugin
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Admin class for My Accessibility Plugin
 *
 * @since 1.0.0
 */
class MAP_Admin {

    /**
     * Admin settings instance
     *
     * @var MAP_Admin_Settings
     * @since 1.0.0
     */
    public $settings;

    /**
     * Constructor
     *
     * @since 1.0.0
     */
    public function __construct() {
        $this->init();
    }

    /**
     * Initialize admin functionality
     *
     * @since 1.0.0
     */
    private function init() {
        // Initialize admin settings
        $this->settings = new MAP_Admin_Settings();
        
        // Add hooks
        $this->add_hooks();
    }

    /**
     * Add WordPress hooks
     *
     * @since 1.0.0
     */
    private function add_hooks() {
        // Add admin menu
        add_action('admin_menu', array($this, 'add_admin_menu'));
        
        // Add admin notices
        add_action('admin_notices', array($this, 'admin_notices'));
        
        // Add dashboard widget
        add_action('wp_dashboard_setup', array($this, 'add_dashboard_widget'));
        
        // Add admin bar menu
        add_action('admin_bar_menu', array($this, 'add_admin_bar_menu'), 100);
        
        // Handle admin actions
        add_action('admin_init', array($this, 'handle_admin_actions'));
    }

    /**
     * Add admin menu
     *
     * @since 1.0.0
     */
    public function add_admin_menu() {
        add_options_page(
            __('My Accessibility Plugin', MAP_TEXT_DOMAIN),
            __('Accessibility', MAP_TEXT_DOMAIN),
            'manage_options',
            'my-accessibility-plugin',
            array($this->settings, 'render_settings_page')
        );
    }

    /**
     * Add admin notices
     *
     * @since 1.0.0
     */
    public function admin_notices() {
        // Check if plugin is properly configured
        $settings = MAP_Core::get_instance()->get_settings();
        
        if (!$settings['text_to_speech_enabled']) {
            ?>
            <div class="notice notice-warning is-dismissible">
                <p>
                    <?php 
                    printf(
                        __('My Accessibility Plugin is installed but text-to-speech is disabled. <a href="%s">Configure settings</a>', MAP_TEXT_DOMAIN),
                        admin_url('options-general.php?page=my-accessibility-plugin')
                    ); 
                    ?>
                </p>
            </div>
            <?php
        }
        
        // Show success message after settings save
        if (isset($_GET['settings-updated']) && $_GET['settings-updated'] === 'true') {
            ?>
            <div class="notice notice-success is-dismissible">
                <p><?php esc_html_e('Settings saved successfully!', MAP_TEXT_DOMAIN); ?></p>
            </div>
            <?php
        }
    }

    /**
     * Add dashboard widget
     *
     * @since 1.0.0
     */
    public function add_dashboard_widget() {
        wp_add_dashboard_widget(
            'map_dashboard_widget',
            __('Accessibility Status', MAP_TEXT_DOMAIN),
            array($this, 'render_dashboard_widget')
        );
    }

    /**
     * Render dashboard widget
     *
     * @since 1.0.0
     */
    public function render_dashboard_widget() {
        $settings = MAP_Core::get_instance()->get_settings();
        ?>
        <div class="map-dashboard-widget">
            <div class="map-status-item">
                <span class="map-status-label"><?php esc_html_e('Text-to-Speech:', MAP_TEXT_DOMAIN); ?></span>
                <span class="map-status-value <?php echo $settings['text_to_speech_enabled'] ? 'enabled' : 'disabled'; ?>">
                    <?php echo $settings['text_to_speech_enabled'] ? __('Enabled', MAP_TEXT_DOMAIN) : __('Disabled', MAP_TEXT_DOMAIN); ?>
                </span>
            </div>
            
            <div class="map-status-item">
                <span class="map-status-label"><?php esc_html_e('Widget Position:', MAP_TEXT_DOMAIN); ?></span>
                <span class="map-status-value"><?php echo esc_html(ucwords(str_replace('-', ' ', $settings['widget_position']))); ?></span>
            </div>
            
            <div class="map-quick-actions">
                <a href="<?php echo admin_url('options-general.php?page=my-accessibility-plugin'); ?>" class="button button-primary">
                    <?php esc_html_e('Settings', MAP_TEXT_DOMAIN); ?>
                </a>
                
                <a href="<?php echo home_url(); ?>" class="button" target="_blank">
                    <?php esc_html_e('View Frontend', MAP_TEXT_DOMAIN); ?>
                </a>
            </div>
        </div>
        
        <style>
            .map-dashboard-widget .map-status-item {
                display: flex;
                justify-content: space-between;
                margin-bottom: 10px;
                padding: 8px 0;
                border-bottom: 1px solid #f0f0f1;
            }
            
            .map-status-label {
                font-weight: 600;
            }
            
            .map-status-value.enabled {
                color: #00a32a;
            }
            
            .map-status-value.disabled {
                color: #d63638;
            }
            
            .map-quick-actions {
                margin-top: 15px;
                text-align: center;
            }
            
            .map-quick-actions .button {
                margin: 0 5px;
            }
        </style>
        <?php
    }

    /**
     * Add admin bar menu
     *
     * @param WP_Admin_Bar $wp_admin_bar
     * @since 1.0.0
     */
    public function add_admin_bar_menu($wp_admin_bar) {
        if (!current_user_can('manage_options')) {
            return;
        }
        
        $settings = MAP_Core::get_instance()->get_settings();
        
        $wp_admin_bar->add_node(array(
            'id' => 'map-accessibility',
            'title' => __('Accessibility', MAP_TEXT_DOMAIN),
            'href' => admin_url('options-general.php?page=my-accessibility-plugin'),
            'meta' => array(
                'title' => __('My Accessibility Plugin Settings', MAP_TEXT_DOMAIN)
            )
        ));
        
        // Add status indicator
        $status_text = $settings['text_to_speech_enabled'] ? __('Enabled', MAP_TEXT_DOMAIN) : __('Disabled', MAP_TEXT_DOMAIN);
        $status_class = $settings['text_to_speech_enabled'] ? 'enabled' : 'disabled';
        
        $wp_admin_bar->add_node(array(
            'parent' => 'map-accessibility',
            'id' => 'map-status',
            'title' => sprintf(__('Status: %s', MAP_TEXT_DOMAIN), $status_text),
            'meta' => array(
                'class' => 'map-status-' . $status_class
            )
        ));
        
        $wp_admin_bar->add_node(array(
            'parent' => 'map-accessibility',
            'id' => 'map-settings',
            'title' => __('Settings', MAP_TEXT_DOMAIN),
            'href' => admin_url('options-general.php?page=my-accessibility-plugin')
        ));
    }

    /**
     * Handle admin actions
     *
     * @since 1.0.0
     */
    public function handle_admin_actions() {
        // Handle reset settings
        if (isset($_POST['map_reset_settings']) && wp_verify_nonce($_POST['_wpnonce'], 'map_reset_settings')) {
            if (current_user_can('manage_options')) {
                MAP_Core::get_instance()->settings->reset_settings();
                wp_redirect(add_query_arg('settings-updated', 'true', admin_url('options-general.php?page=my-accessibility-plugin')));
                exit;
            }
        }
        
        // Handle export settings
        if (isset($_POST['map_export_settings']) && wp_verify_nonce($_POST['_wpnonce'], 'map_export_settings')) {
            if (current_user_can('manage_options')) {
                $settings = MAP_Core::get_instance()->settings->export_settings();
                
                header('Content-Type: application/json');
                header('Content-Disposition: attachment; filename="map-settings-' . date('Y-m-d') . '.json"');
                echo $settings;
                exit;
            }
        }
        
        // Handle import settings
        if (isset($_POST['map_import_settings']) && wp_verify_nonce($_POST['_wpnonce'], 'map_import_settings')) {
            if (current_user_can('manage_options') && isset($_FILES['settings_file'])) {
                $file_content = file_get_contents($_FILES['settings_file']['tmp_name']);
                
                if (MAP_Core::get_instance()->settings->import_settings($file_content)) {
                    wp_redirect(add_query_arg('settings-updated', 'true', admin_url('options-general.php?page=my-accessibility-plugin')));
                } else {
                    wp_redirect(add_query_arg('import-error', 'true', admin_url('options-general.php?page=my-accessibility-plugin')));
                }
                exit;
            }
        }
    }

    /**
     * Get plugin statistics (for future dashboard enhancements)
     *
     * @return array
     * @since 1.0.0
     */
    public function get_plugin_stats() {
        return array(
            'total_uses' => 0,
            'active_features' => 1,
            'last_used' => null
        );
    }
}
