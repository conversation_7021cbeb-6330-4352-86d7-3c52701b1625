/* ===== PREMIUM FOCUS STYLES - KEYBOARD ONLY ===== */
.map-focused,
.map-accessibility-widget *:focus-visible {
    outline: none;
    box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.3);
    border-radius: var(--map-radius-sm);
    scroll-margin: 20px; /* Prevent focus scroll from causing layout issues */
}

.map-accessibility-widget button:focus-visible {
    outline: none;
    box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.3);
    scroll-margin: 20px; /* Prevent focus scroll from causing layout issues */
}

/* Prevent focus-induced scroll issues in panel content */
.map-panel-content *:focus {
    scroll-margin-top: 20px;
    scroll-margin-bottom: 20px;
}

/* Text Highlighting */
.map-highlight-text {
    background-color: #ffff99 !important;
    color: #000 !important;
    transition: background-color 0.3s ease;
}

/* Responsive Design */
@media (max-width: 480px) {
    .map-widget-panel {
        width: 380px; /* Increased from 340px */
        height: 600px; /* Significantly increased for maximum bottom space */
    }

    .map-panel-content {
        padding: 16px;
    }

    .map-position-center {
        position: fixed;
        top: 15px; /* Reduced from 20px to move menu up more */
        left: 10px;
        right: 10px;
        transform: none;
    }

    .map-position-center .map-widget-panel {
        width: 100%;
        left: 0;
        transform: none;
    }
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
    :root {
        --map-gray-200: #000000;
        --map-border-color: #000;
        --map-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
        --map-shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.3), 0 2px 4px -1px rgba(0, 0, 0, 0.2);
    }

    .map-widget-panel {
        border-width: 2px;
        border-color: #000000;
    }

    .map-feature-toggle {
        border-width: 2px;
    }
}

/* Consolidated Reduced Motion - Accessibility Support */
@media (prefers-reduced-motion: reduce) {
    * {
        transition: none !important;
        animation: none !important;
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
        scroll-behavior: auto !important;
    }

    /* Modal view simplified transitions */
    .map-modal-view,
    .map-modal-view.map-view-entering,
    .map-modal-view.map-view-entered,
    .map-modal-view.map-view-exiting,
    .map-modal-view.map-view-entering-forward,
    .map-modal-view.map-view-entering-backward,
    .map-modal-view.map-view-exiting-forward,
    .map-modal-view.map-view-exiting-backward {
        transition: opacity 0.2s ease !important;
        transform: none !important;
        animation: fadeIn 0.2s ease forwards !important;
    }

    /* Category and button transforms disabled */
    .map-category-button:hover:not(.map-category-disabled),
    .map-back-button:hover {
        transform: none !important;
    }

    /* Reading guide simplified */
    .map-reading-guide,
    .map-reading-guide.smooth {
        transition: opacity 0.2s ease-in-out !important;
    }

    /* ADHD focus effects disabled */
    #map-adhd-focus-border {
        transition: none !important;
        backdrop-filter: none !important;
        -webkit-backdrop-filter: none !important;
    }

    @keyframes fadeIn {
        from { opacity: 0; }
        to { opacity: 1; }
    }

    @keyframes fadeOut {
        from { opacity: 1; }
        to { opacity: 0; }
    }
}

/* Responsive Design for Clean Headers */
@media (max-width: 768px) {
    .map-contrast-themes-header .map-collapsible-header,
    .map-studio-header {
        flex-direction: column;
        align-items: flex-start;
        gap: var(--map-space-3);
        padding: var(--map-space-4);
    }

    .map-contrast-themes-info,
    .map-custom-theme-info {
        width: 100%;
    }

    .map-contrast-themes-current {
        align-self: flex-end;
        min-width: 60px;
        height: 26px;
        padding: 4px 8px;
        font-size: 12px;
    }

    .map-contrast-themes-icon,
    .map-studio-icon {
        width: 32px;
        height: 32px;
    }

    .map-contrast-themes-title,
    .map-custom-theme-title {
        font-size: 15px;
    }

    .map-contrast-themes-desc,
    .map-custom-theme-desc {
        font-size: 13px;
    }

    .map-contrast-themes-grid {
        grid-template-columns: repeat(3, 1fr);
        gap: var(--map-space-2);
        padding: var(--map-space-4);
    }

    /* Color Studio Responsive */
    .map-studio-grid {
        grid-template-columns: 1fr;
        gap: var(--map-space-4);
    }

    .map-color-card {
        padding: var(--map-space-4);
    }

    .map-color-card-header {
        gap: var(--map-space-2);
        margin-bottom: var(--map-space-3);
    }

    .map-color-card-icon {
        padding: var(--map-space-2);
    }

    .map-color-card-title {
        font-size: 15px;
    }

    .map-color-card-desc {
        font-size: 12px;
    }

    .map-color-picker-wrapper {
        flex-direction: column;
        gap: var(--map-space-3);
        padding: var(--map-space-3);
    }

    .map-studio-picker {
        width: 50px;
        height: 50px;
    }

    .map-studio-content {
        padding: var(--map-space-4);
    }

    .map-custom-theme-title {
        font-size: 16px;
    }

    .map-custom-theme-desc {
        font-size: 13px;
    }

    .map-color-picker {
        width: 50px;
        height: 35px;
    }

    .map-theme-button {
        min-height: 100px;
        padding: var(--map-space-3);
    }

    .map-theme-preview {
        width: 50px;
        height: 32px;
    }

    .map-theme-name {
        font-size: 12px;
    }

    .map-theme-check {
        width: 18px;
        height: 18px;
    }

    .map-theme-check svg {
        width: 10px;
        height: 10px;
    }
}

@media (max-width: 480px) {
    .map-contrast-themes-grid {
        grid-template-columns: 1fr;
        gap: var(--map-space-2);
    }

    .map-theme-button {
        flex-direction: row;
        min-height: 60px;
        padding: var(--map-space-3);
        gap: var(--map-space-3);
    }

    .map-theme-preview {
        width: 40px;
        height: 28px;
        flex-shrink: 0;
    }

    .map-theme-info {
        flex: 1;
        justify-content: space-between;
    }

    .map-theme-name {
        text-align: left;
        font-size: 13px;
    }
}

/* ===== RESPONSIVE DESIGN FOR MODAL SYSTEM ===== */

/* Large screens - Extra spacious */
@media (min-width: 1200px) {
    .map-widget-panel {
        width: 520px; /* Even larger on big screens */
        height: 750px; /* Maximum height for large screens */
    }

    /* Better positioning on large screens */
    .map-position-bottom-right .map-widget-panel,
    .map-position-bottom-left .map-widget-panel {
        bottom: -10px; /* Moved up more for large screens */
    }

    .map-position-top-right .map-widget-panel,
    .map-position-top-left .map-widget-panel {
        margin-top: 15px; /* Reduced margin for closer positioning */
    }

    /* Move right panels optimally on large screens */
    .map-position-bottom-right .map-widget-panel,
    .map-position-top-right .map-widget-panel {
        right: -15px; /* Optimal positioning to show all corners */
    }
}
/* Tablet and smaller screens */
@media (max-width: 768px) {
    .map-widget-panel {
        width: 450px; /* Increased from 400px */
        height: 650px; /* Significantly increased for maximum bottom space */
    }

    .map-panel-content {
        height: calc(650px - 80px);
    }

    .map-modal-views-container {
        height: calc(650px - 80px);
    }

    .map-category-button {
        padding: var(--map-space-3);
        min-height: 64px;
    }

    .map-category-icon {
        width: 40px;
        height: 40px;
        margin-right: var(--map-space-3);
    }
    .map-view-header {
        margin-bottom: var(--map-space-4);
        padding-bottom: var(--map-space-3);
    }

    .map-back-button {
        padding: var(--map-space-2);
        margin-right: var(--map-space-3);
    }

    .map-view-title {
        font-size: var(--map-font-size-base);
    }

    .map-header-category-title {
        font-size: var(--map-font-size-base);
    }
}

/* Mobile screens */
@media (max-width: 480px) {
    .map-widget-panel {
        width: 380px; /* Increased from 360px */
        height: 600px; /* Significantly increased for maximum bottom space */
    }

    .map-panel-content {
        height: calc(600px - 70px);
        padding: var(--map-space-4);
    }

    .map-modal-views-container {
        height: calc(600px - 70px);
    }

    .map-category-grid {
        gap: var(--map-space-2);
    }

    .map-category-button {
        padding: var(--map-space-3);
        min-height: 60px;
    }

    .map-category-icon {
        width: 36px;
        height: 36px;
        margin-right: var(--map-space-3);
    }

    /* Emoji styling removed - now using premium SVG icons */

    .map-category-title {
        font-size: var(--map-font-size-sm);
    }

    .map-category-desc {
        font-size: 12px;
    }

    .map-back-button {
        padding: var(--map-space-1) var(--map-space-2);
        font-size: 12px;
    }

    .map-back-button svg {
        width: 14px;
        height: 14px;
    }

    .map-view-title {
        font-size: var(--map-font-size-sm);
    }

    .map-header-category-title {
        font-size: var(--map-font-size-sm);
    }

    .map-placeholder-section {
        padding: var(--map-space-4);
    }

    .map-placeholder-icon {
        font-size: 24px;
        margin-right: var(--map-space-3);
    }

    /* Center position adjustments for mobile */
    .map-position-center {
        position: fixed;
        top: 10px;
        left: 10px;
        right: 10px;
        transform: none;
    }

    .map-position-center .map-widget-panel {
        width: 100%;
        left: 0;
        transform: none;
        max-height: calc(100vh - 100px);
        height: auto; /* Allow flexible height for very small screens */
    }
}

/* High contrast mode enhancements */
@media (prefers-contrast: high) {
    .map-category-button {
        border-width: 2px;
    }

    .map-category-button:hover:not(.map-category-disabled) {
        border-width: 3px;
    }

    .map-back-button {
        border-width: 2px;
    }

    .map-placeholder-section {
        border-width: 2px;
    }
}
