/* ===== CONTRAST THEMES SECTION - UNIFIED WITH TEXT CATEGORY ===== */

/* Visual Themes and Color Studio now use standard map-feature-toggle styling */
/* All styling handled by existing .map-feature-toggle, .map-feature-controls classes */

/* ===== COMPACT THEME SELECTOR - MATCHING TEXT CATEGORY STYLE ===== */

/* Theme Selector Container - Compact Design */
.map-theme-selector {
    padding: var(--map-space-4);
    background: var(--map-white);
    border-radius: var(--map-radius-md);
    pointer-events: auto;
    position: relative;
    border: 1px solid var(--map-gray-200);
}

/* Theme Counter - Removed for cleaner design */

/* Theme Preview Card - Compact Style */
.map-theme-preview-card {
    position: relative;
    background: var(--map-white);
    border-radius: var(--map-radius-lg);
    padding: var(--map-space-4);
    margin-bottom: var(--map-space-3);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    border: 1px solid var(--map-gray-200);
    transition: all var(--map-transition-base);
}

.map-theme-preview-card:hover {
    transform: translateY(-2px);
    box-shadow:
        0 10px 15px -3px rgba(0, 0, 0, 0.1),
        0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

/* Navigation Arrows */
/* Navigation Arrows - Compact Style */
.map-theme-nav {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    width: 36px;
    height: 36px;
    border: 1px solid var(--map-gray-300);
    border-radius: 50%;
    background: var(--map-white);
    color: var(--map-gray-600);
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
    transition: all var(--map-transition-base);
    z-index: 2;
}

.map-theme-nav:hover {
    background: var(--map-primary);
    color: var(--map-white);
    transform: translateY(-50%) scale(1.05);
    box-shadow: 0 4px 8px rgba(99, 102, 241, 0.2);
}

.map-theme-nav:active {
    transform: translateY(-50%) scale(0.95);
}

.map-theme-nav-prev {
    left: -18px;
}

.map-theme-nav-next {
    right: -18px;
}

/* Theme Icon Preview */
.map-theme-icon-preview-container {
    display: flex;
    justify-content: center;
    align-items: center;
    margin-bottom: var(--map-space-4);
}

.map-theme-icon-preview {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 48px;
    height: 48px;
    border-radius: 50%;
    background: linear-gradient(135deg, var(--map-primary) 0%, #5b21b6 100%);
    color: var(--map-white);
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow: 0 3px 10px rgba(99, 102, 241, 0.3);
    position: relative;
    overflow: hidden;
}

.map-theme-icon-preview::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.2) 0%, transparent 50%);
    border-radius: 50%;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.map-theme-icon-preview:hover::before {
    opacity: 1;
}

.map-theme-icon-preview svg {
    width: 28px;
    height: 28px;
    transition: transform 0.3s ease;
}

.map-theme-icon-preview:hover svg {
    transform: scale(1.1);
}

/* Theme-specific icon preview colors */
.map-theme-icon-preview[data-theme="normal"] {
    background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
    box-shadow: 0 4px 15px rgba(59, 130, 246, 0.3);
}

.map-theme-icon-preview[data-theme="monochrome"] {
    background: linear-gradient(135deg, #6b7280 0%, #374151 100%);
    box-shadow: 0 4px 15px rgba(107, 114, 128, 0.3);
}

.map-theme-icon-preview[data-theme="low-saturation"] {
    background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);
    box-shadow: 0 4px 15px rgba(139, 92, 246, 0.3);
}

.map-theme-icon-preview[data-theme="high-saturation"] {
    background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
    box-shadow: 0 4px 15px rgba(245, 158, 11, 0.3);
}

.map-theme-icon-preview[data-theme="dark"] {
    background: linear-gradient(135deg, #1f2937 0%, #111827 100%);
    box-shadow: 0 4px 15px rgba(31, 41, 55, 0.4);
}

.map-theme-icon-preview[data-theme="high-contrast"] {
    background: linear-gradient(135deg, #000000 0%, #374151 100%);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
}

.map-theme-icon-preview[data-theme="sepia"] {
    background: linear-gradient(135deg, #92400e 0%, #78350f 100%);
    box-shadow: 0 4px 15px rgba(146, 64, 14, 0.3);
}

.map-theme-icon-preview[data-theme="colorblind"] {
    background: linear-gradient(135deg, #059669 0%, #047857 100%);
    box-shadow: 0 4px 15px rgba(5, 150, 105, 0.3);
}

/* Theme transition animations */
@keyframes themeSlideIn {
    from {
        opacity: 0;
        transform: translateX(20px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes iconPulse {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.1);
    }
    100% {
        transform: scale(1);
    }
}

.map-theme-info {
    animation: themeSlideIn 0.4s ease-out;
}

.map-theme-icon-preview {
    animation: iconPulse 0.6s ease-out;
}

/* Active theme glow effect */
.map-theme-icon-preview.applying {
    animation: applyingGlow 1s ease-in-out;
}

@keyframes applyingGlow {
    0%, 100% {
        box-shadow: 0 3px 10px rgba(99, 102, 241, 0.3);
    }
    50% {
        box-shadow: 0 6px 20px rgba(99, 102, 241, 0.6), 0 0 25px rgba(99, 102, 241, 0.4);
        transform: scale(1.08);
    }
}

/* Theme Info */
/* Theme Info - Perfectly Centered Under Icon */
.map-theme-info {
    display: flex;
    justify-content: center;
    align-items: center;
    text-align: center;
    margin: var(--map-space-3) 0 var(--map-space-2) 0;
}

.map-theme-title {
    font-size: var(--map-font-size-base);
    font-weight: 600;
    color: var(--map-gray-800);
    margin: 0;
    letter-spacing: -0.025em;
    text-align: center;
    width: 100%;
}

/* Theme Dots Indicator - No Extra Space */
.map-theme-dots {
    display: flex;
    justify-content: center;
    gap: var(--map-space-2);
    margin: 0;
}

.map-theme-dot {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    border: 2px solid var(--map-gray-300);
    background: var(--map-white);
    cursor: pointer;
    transition: all 0.2s ease;
    position: relative;
}

.map-theme-dot:hover {
    border-color: var(--map-primary);
    transform: scale(1.2);
}

.map-theme-dot.active {
    background: var(--map-primary);
    border-color: var(--map-primary);
    box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.2);
}

/* Default subtle focus for theme dot */
.map-theme-dot:focus {
    outline: 2px solid var(--map-gray-400);
    outline-offset: 1px;
}

/* ===== RESPONSIVE DESIGN FOR THEME SELECTOR ===== */

@media (max-width: 768px) {
    .map-theme-selector {
        padding: var(--map-space-4);
    }

    .map-theme-preview-card {
        padding: var(--map-space-4);
        margin-bottom: var(--map-space-4);
    }

    .map-theme-nav {
        width: 36px;
        height: 36px;
    }

    .map-theme-nav-prev {
        left: -18px;
    }

    .map-theme-nav-next {
        right: -18px;
    }

    .map-theme-title {
        font-size: var(--map-font-size-base);
    }

    .map-theme-desc {
        font-size: var(--map-font-size-xs);
    }

    .map-theme-dot {
        width: 10px;
        height: 10px;
    }

    .map-theme-icon-preview {
        width: 42px;
        height: 42px;
    }

    .map-theme-icon-preview svg {
        width: 24px;
        height: 24px;
    }
}

@media (max-width: 480px) {
    .map-theme-selector {
        padding: var(--map-space-3);
    }

    .map-theme-preview-card {
        padding: var(--map-space-3);
    }

    .map-theme-nav {
        width: 32px;
        height: 32px;
    }

    .map-theme-nav-prev {
        left: -16px;
    }

    .map-theme-nav-next {
        right: -16px;
    }

    .map-theme-counter {
        font-size: var(--map-font-size-xs);
    }
}

/* Legacy theme button styles removed - using new theme selector design */

.map-theme-button:hover {
    transform: translateY(-2px);
    box-shadow: var(--map-shadow-lg);
    border-color: var(--map-primary-light);
}

.map-theme-button.map-theme-active {
    border-color: var(--map-primary);
    background: linear-gradient(135deg, rgba(99, 102, 241, 0.05) 0%, rgba(129, 140, 248, 0.05) 100%);
    box-shadow: var(--map-shadow-md);
}

.map-theme-button.map-theme-active::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, var(--map-primary) 0%, var(--map-primary-light) 100%);
}

/* Theme Preview */
.map-theme-preview {
    width: 60px;
    height: 40px;
    border-radius: var(--map-radius-md);
    overflow: hidden;
    position: relative;
    box-shadow: var(--map-shadow-sm);
    border: 1px solid var(--map-gray-200);
}

.map-theme-preview-bg {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: var(--map-white);
}

.map-theme-preview-content {
    position: absolute;
    top: 6px;
    left: 6px;
    right: 6px;
    bottom: 6px;
    display: flex;
    flex-direction: column;
    gap: 2px;
}

.map-theme-preview-text {
    height: 3px;
    background: var(--map-gray-700);
    border-radius: 1px;
    width: 80%;
}

.map-theme-preview-accent {
    height: 2px;
    background: var(--map-primary);
    border-radius: 1px;
    width: 60%;
}

/* Dark Theme Preview */
.map-theme-preview-dark .map-theme-preview-bg {
    background: #1a1a1a;
}

.map-theme-preview-dark .map-theme-preview-text {
    background: #ffffff;
}

.map-theme-preview-dark .map-theme-preview-accent {
    background: #60a5fa;
}

/* High Contrast Theme Preview */
.map-theme-preview-high-contrast .map-theme-preview-bg {
    background: #000000;
}

.map-theme-preview-high-contrast .map-theme-preview-text {
    background: #ffffff;
}

.map-theme-preview-high-contrast .map-theme-preview-accent {
    background: #ffffff;
}

/* Sepia Theme Preview */
.map-theme-preview-sepia .map-theme-preview-bg {
    background: #f4f1e8;
}

.map-theme-preview-sepia .map-theme-preview-text {
    background: #5c4b37;
}

.map-theme-preview-sepia .map-theme-preview-accent {
    background: #8b6914;
}

/* Monochrome Theme Preview */
.map-theme-preview-monochrome .map-theme-preview-bg {
    background: #f8f9fa;
}

.map-theme-preview-monochrome .map-theme-preview-text {
    background: #6c757d; /* Medium gray */
}

.map-theme-preview-monochrome .map-theme-preview-accent {
    background: #495057; /* Dark gray */
}

/* Low Saturation Theme Preview */
.map-theme-preview-low-saturation .map-theme-preview-bg {
    background: #ffffff;
}

.map-theme-preview-low-saturation .map-theme-preview-text {
    background: #6b7280; /* Desaturated gray */
}

.map-theme-preview-low-saturation .map-theme-preview-accent {
    background: #9ca3af; /* Muted accent */
}

/* High Saturation Theme Preview */
.map-theme-preview-high-saturation .map-theme-preview-bg {
    background: #ffffff;
}

.map-theme-preview-high-saturation .map-theme-preview-text {
    background: #1e40af; /* Vibrant blue */
}

.map-theme-preview-high-saturation .map-theme-preview-accent {
    background: #dc2626; /* Vibrant red */
}

/* Color Blind Theme Preview */
.map-theme-preview-colorblind .map-theme-preview-bg {
    background: #fafafa;
}

.map-theme-preview-colorblind .map-theme-preview-text {
    background: #2563eb;
}

.map-theme-preview-colorblind .map-theme-preview-accent {
    background: #dc2626;
}

/* Theme Info */
.map-theme-info {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    gap: var(--map-space-2);
}

.map-theme-name {
    font-size: var(--map-font-size-sm);
    font-weight: 600;
    color: var(--map-gray-700);
    text-align: center;
    flex: 1;
    line-height: 1.3;
}

.map-theme-active .map-theme-name {
    color: var(--map-primary);
}

.map-theme-check {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background: var(--map-gray-200);
    color: var(--map-white);
    opacity: 0;
    transition: all var(--map-transition-base);
    flex-shrink: 0;
}

.map-theme-active .map-theme-check {
    background: var(--map-primary);
    opacity: 1;
    transform: scale(1.1);
}

.map-theme-check svg {
    width: 12px;
    height: 12px;
}

/* Theme Application Classes */
body.map-theme-dark {
    background-color: #1a1a1a !important;
    color: #ffffff !important;
}

body.map-theme-dark * {
    background-color: inherit !important;
    color: inherit !important;
    border-color: #404040 !important;
}

body.map-theme-dark a {
    color: #60a5fa !important;
}

body.map-theme-high-contrast {
    background-color: #000000 !important;
    color: #ffffff !important;
}

body.map-theme-high-contrast * {
    background-color: #000000 !important;
    color: #ffffff !important;
    border-color: #ffffff !important;
}

body.map-theme-high-contrast a {
    color: #ffffff !important;
    text-decoration: underline !important;
}

body.map-theme-sepia {
    background-color: #f4f1e8 !important;
    color: #5c4b37 !important;
}

body.map-theme-sepia * {
    background-color: inherit !important;
    color: inherit !important;
    border-color: #d4c5a9 !important;
}

body.map-theme-sepia a {
    color: #8b6914 !important;
}

/* Monochrome Theme - Convert everything to grayscale */
body.map-theme-monochrome main,
body.map-theme-monochrome article,
body.map-theme-monochrome section,
body.map-theme-monochrome div:not(.map-accessibility-widget),
body.map-theme-monochrome header,
body.map-theme-monochrome footer,
body.map-theme-monochrome nav,
body.map-theme-monochrome aside,
body.map-theme-monochrome p,
body.map-theme-monochrome h1,
body.map-theme-monochrome h2,
body.map-theme-monochrome h3,
body.map-theme-monochrome h4,
body.map-theme-monochrome h5,
body.map-theme-monochrome h6,
body.map-theme-monochrome img,
body.map-theme-monochrome video,
body.map-theme-monochrome canvas,
body.map-theme-monochrome svg {
    filter: grayscale(1) !important;
}

/* Low Saturation Theme - Target main content areas to avoid positioning issues */
body.map-theme-low-saturation main,
body.map-theme-low-saturation article,
body.map-theme-low-saturation section,
body.map-theme-low-saturation div:not(.map-accessibility-widget),
body.map-theme-low-saturation header,
body.map-theme-low-saturation footer,
body.map-theme-low-saturation nav,
body.map-theme-low-saturation aside,
body.map-theme-low-saturation p,
body.map-theme-low-saturation h1,
body.map-theme-low-saturation h2,
body.map-theme-low-saturation h3,
body.map-theme-low-saturation h4,
body.map-theme-low-saturation h5,
body.map-theme-low-saturation h6,
body.map-theme-low-saturation img,
body.map-theme-low-saturation video,
body.map-theme-low-saturation canvas,
body.map-theme-low-saturation svg {
    filter: saturate(0.88) !important;
}

/* High Saturation Theme - Target main content areas to avoid positioning issues */
body.map-theme-high-saturation main,
body.map-theme-high-saturation article,
body.map-theme-high-saturation section,
body.map-theme-high-saturation div:not(.map-accessibility-widget),
body.map-theme-high-saturation header,
body.map-theme-high-saturation footer,
body.map-theme-high-saturation nav,
body.map-theme-high-saturation aside,
body.map-theme-high-saturation p,
body.map-theme-high-saturation h1,
body.map-theme-high-saturation h2,
body.map-theme-high-saturation h3,
body.map-theme-high-saturation h4,
body.map-theme-high-saturation h5,
body.map-theme-high-saturation h6,
body.map-theme-high-saturation img,
body.map-theme-high-saturation video,
body.map-theme-high-saturation canvas,
body.map-theme-high-saturation svg {
    filter: saturate(1.25) !important;
}

/* Ensure accessibility widget is never affected by any filters */
.map-accessibility-widget,
.map-accessibility-widget *,
.map-accessibility-widget .map-widget-panel,
.map-accessibility-widget .map-main-toggle {
    filter: none !important;
}

/* Ensure monochrome theme doesn't affect the accessibility widget */
body.map-theme-monochrome .map-accessibility-widget,
body.map-theme-monochrome .map-accessibility-widget * {
    filter: none !important;
}

/* ===== DARK MODE THEME NAVIGATION FIXES ===== */

/* Ensure theme preview card maintains proper positioning in dark mode */
.map-accessibility-widget.map-dark-mode .map-theme-preview-card {
    position: relative !important;
    background: var(--map-gray-100);
    border: 1px solid var(--map-border);
    box-shadow: var(--map-shadow-md);
}

/* Fix navigation buttons positioning - ensure they stay outside the card in dark mode */
.map-accessibility-widget.map-dark-mode .map-theme-nav {
    position: absolute !important;
    top: 50% !important;
    transform: translateY(-50%) !important;
    background: var(--map-gray-200);
    border: 1px solid var(--map-border);
    color: var(--map-text);
    box-shadow: var(--map-shadow-sm);
    z-index: 2 !important;
}

.map-accessibility-widget.map-dark-mode .map-theme-nav-prev {
    left: -18px !important; /* Force outside positioning */
}

.map-accessibility-widget.map-dark-mode .map-theme-nav-next {
    right: -18px !important; /* Force outside positioning */
}

.map-accessibility-widget.map-dark-mode .map-theme-nav:hover {
    background: var(--map-primary);
    color: var(--map-white);
    transform: translateY(-50%) scale(1.05) !important;
    box-shadow: 0 4px 8px rgba(124, 58, 237, 0.3);
}

/* ===== DARK MODE THEME PREVIEW ENHANCEMENTS ===== */

/* Enhanced theme icon preview for dark mode */
.map-accessibility-widget.map-dark-mode .map-theme-icon-preview {
    background: linear-gradient(135deg, var(--map-primary) 0%, var(--map-primary-dark) 100%);
    box-shadow: 0 4px 15px rgba(124, 58, 237, 0.4),
                0 0 20px rgba(124, 58, 237, 0.2);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

/* Theme info text styling for dark mode */
.map-accessibility-widget.map-dark-mode .map-theme-title {
    color: var(--map-text);
    font-weight: 600;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

/* Theme dots styling for dark mode */
.map-accessibility-widget.map-dark-mode .map-theme-dot {
    background: var(--map-gray-400);
    border: 1px solid var(--map-border);
    box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.1);
}

.map-accessibility-widget.map-dark-mode .map-theme-dot.active {
    background: var(--map-primary);
    border-color: var(--map-primary);
    box-shadow: 0 0 8px rgba(124, 58, 237, 0.4),
                inset 0 1px 2px rgba(255, 255, 255, 0.1);
}

/* Enhanced theme selector container for dark mode - Fix white background */
.map-accessibility-widget.map-dark-mode .map-theme-selector {
    background: var(--map-gray-100) !important; /* Override white background */
    border-radius: var(--map-radius-lg);
    padding: var(--map-space-4);
    border: 1px solid var(--map-border);
    box-shadow: var(--map-shadow-md);
}

/* Fix any other white containers in theme section */
.map-accessibility-widget.map-dark-mode .map-custom-theme-content {
    background: var(--map-gray-100) !important;
    border: 1px solid var(--map-border);
}

/* Ensure theme preview background is also dark */
.map-accessibility-widget.map-dark-mode .map-theme-preview-bg {
    background: var(--map-gray-200) !important;
}

/* Fix theme preview text colors for dark mode */
.map-accessibility-widget.map-dark-mode .map-theme-preview-text {
    background: var(--map-text) !important;
}

.map-accessibility-widget.map-dark-mode .map-theme-preview-accent {
    background: var(--map-primary) !important;
}

/* ===== COMPLETE DARK MODE THEME SECTION FIXES ===== */

/* Ensure all theme-related containers are dark */
.map-accessibility-widget.map-dark-mode .map-feature-controls {
    background: transparent !important;
}

/* Fix any remaining white backgrounds in theme section */
.map-accessibility-widget.map-dark-mode .map-theme-preview {
    background: var(--map-gray-200) !important;
    border: 1px solid var(--map-border);
}

/* Ensure theme dots container is dark */
.map-accessibility-widget.map-dark-mode .map-theme-dots {
    background: transparent !important;
}

/* Fix theme info container */
.map-accessibility-widget.map-dark-mode .map-theme-info {
    background: transparent !important;
}

/* Ensure consistent dark styling for all theme elements */
.map-accessibility-widget.map-dark-mode .map-theme-icon-preview-container {
    background: transparent !important;
}
