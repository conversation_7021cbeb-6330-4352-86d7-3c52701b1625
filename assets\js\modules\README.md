# JavaScript Modules Structure

This directory contains the modular JavaScript architecture for the My Accessibility Plugin.

## 📁 Directory Structure

```
assets/js/modules/
├── core.js                     # Main MapAccessibility class and core functionality
├── init.js                     # Initialization and instance management
├── ui/
│   └── modal-system.js         # Widget panel, navigation, and modal transitions
├── features/
│   ├── text-to-speech.js       # Speech synthesis and text selection
│   ├── reading-cognitive.js    # Font size, line spacing, dyslexic font, reading guide
│   ├── visual-adjustments.js   # Visual themes and color management
│   ├── text-typography.js      # ADHD focus, big cursor, text magnification
│   └── settings.js             # Dark mode, language, menu position
└── utils/
    ├── translations.js         # Internationalization and language support
    ├── storage.js              # LocalStorage and settings persistence
    ├── reset.js                # Reset functionality and cleanup
    └── keyboard.js             # Keyboard shortcuts and navigation
```

## 🔄 Module Dependencies

### Loading Order:
1. **Core utilities** (translations, storage, keyboard)
2. **UI components** (modal-system)
3. **Feature modules** (text-to-speech, visual-adjustments, reading-cognitive, text-typography, settings)
4. **Reset and initialization**

### Module Relationships:
- `core.js` - Main class that coordinates all other modules
- `utils/*` - Utility modules used by feature modules
- `features/*` - Independent feature modules that extend core functionality
- `ui/modal-system.js` - UI management used by all feature modules
- `init.js` - Initialization that ties everything together

## 📋 Migration Plan

1. **Phase 1**: Split the large frontend.js into these modules
2. **Phase 2**: Create frontend-modular.js as the new entry point
3. **Phase 3**: Update PHP enqueue scripts to load modular version
4. **Phase 4**: Test and validate all functionality
5. **Phase 5**: Remove old frontend.js file

## 🎯 Benefits

- **Maintainability**: Easier to find and modify specific functionality
- **Performance**: Can load only needed modules
- **Collaboration**: Multiple developers can work on different modules
- **Testing**: Easier to unit test individual modules
- **Debugging**: Cleaner stack traces and easier troubleshooting
