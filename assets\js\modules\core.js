/**
 * Core Module - Main MapAccessibility Class and Initialization
 *
 * This module contains:
 * - Main MapAccessibility class constructor
 * - Core initialization logic
 * - Settings management
 * - Event binding coordination
 * - Global instance management
 *
 * @package MyAccessibilityPlugin
 * @since 1.0.0
 */

(function($) {
    'use strict';

    /**
     * Main Accessibility Plugin Class
     */
    class MapAccessibility {
        constructor() {
            this.isPlaying = false;
            this.isPaused = false;
            this.isSpeechInProgress = false;
            this.currentUtterance = null;
            this.speechSynthesis = window.speechSynthesis;
            this.currentText = '';
            this.currentPosition = 0;
            this.highlightEnabled = false;
            this.autoScrollEnabled = false;
            this.isTextToSpeechActive = false;
            this.isDyslexicFontActive = false;
            this.isReadingGuideActive = false;
            this.adhdFocusMode = false;
            this.bigCursorMode = false;
            this.textMagnificationMode = false;
            this.currentFontSize = 100;
            this.currentLineSpacing = 1.5;
            this.currentLetterSpacing = 0;
            this.currentTextAlignment = 'left';
            this.currentCategory = null;
            this.currentContrastTheme = 'normal';

            // Custom theme colors
            this.customThemeColors = {
                text: '',
                background: '',
                link: '',
                heading: ''
            };
            this.isCustomThemeActive = false;
            this.customThemeDebounceTimer = null;

            // Theme selector properties
            this.themes = [
                {
                    id: 'normal',
                    name: 'Default',
                    description: 'Standard website appearance',
                    icon: '<svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor"><path d="M10 20v-6h4v6h5v-8h3L12 3 2 12h3v8z"/></svg>'
                },
                {
                    id: 'monochrome',
                    name: 'Monochrome',
                    description: 'Grayscale colors for reduced visual distraction',
                    icon: '<svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor"><rect x="3" y="6" width="3" height="12" fill="currentColor"/><rect x="7" y="6" width="3" height="12" fill="currentColor" opacity="0.7"/><rect x="11" y="6" width="3" height="12" fill="currentColor" opacity="0.4"/><rect x="15" y="6" width="3" height="12" fill="currentColor" opacity="0.2"/><rect x="19" y="6" width="2" height="12" fill="currentColor" opacity="0.1"/></svg>'
                },
                {
                    id: 'low-saturation',
                    name: 'Low Saturation',
                    description: 'Reduced color intensity for comfortable viewing',
                    icon: '<svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor"><circle cx="12" cy="12" r="8" fill="none" stroke="currentColor" stroke-width="2" opacity="0.4"/><circle cx="12" cy="8" r="2" fill="currentColor" opacity="0.3"/><circle cx="8" cy="14" r="1.5" fill="currentColor" opacity="0.3"/><circle cx="16" cy="14" r="1.5" fill="currentColor" opacity="0.3"/></svg>'
                },
                {
                    id: 'high-saturation',
                    name: 'High Saturation',
                    description: 'Enhanced color intensity for vibrant viewing',
                    icon: '<svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor"><path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/></svg>'
                },
                {
                    id: 'dark',
                    name: 'Dark Mode',
                    description: 'Dark background with light text for low-light environments',
                    icon: '<svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor"><path d="M21 12.79A9 9 0 1 1 11.21 3 7 7 0 0 0 21 12.79z"/></svg>'
                },
                {
                    id: 'high-contrast',
                    name: 'High Contrast',
                    description: 'Black and white for maximum readability',
                    icon: '<svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor"><path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 18V4c4.41 0 8 3.59 8 8s-3.59 8-8 8z"/></svg>'
                },
                {
                    id: 'sepia',
                    name: 'Sepia',
                    description: 'Warm, paper-like colors for comfortable reading',
                    icon: '<svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor"><rect x="3" y="4" width="18" height="14" rx="2" fill="none" stroke="currentColor" stroke-width="1.5"/><circle cx="8" cy="9" r="1.5" fill="currentColor"/><path d="M21 15l-3.5-3.5c-.4-.4-1-.4-1.4 0L6 21" stroke="currentColor" stroke-width="1.5" fill="none"/><rect x="2" y="2" width="20" height="20" fill="none" stroke="currentColor" stroke-width="0.5" opacity="0.3" rx="1"/></svg>'
                },
                {
                    id: 'colorblind',
                    name: 'Color Blind Friendly',
                    description: 'Optimized colors for color vision deficiency',
                    icon: '<svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor"><path d="M12 4.5C7 4.5 2.73 7.61 1 12c1.73 4.39 6 7.5 11 7.5s9.27-3.11 11-7.5c-1.73-4.39-6-7.5-11-7.5z"/><circle cx="12" cy="12" r="3" fill="white"/><path d="M12 9v6M9 12h6" stroke="currentColor" stroke-width="1.5"/></svg>'
                }
            ];
            this.currentThemeIndex = 0;
            this.selectedTheme = 'normal';

            // Dark mode for widget interface
            this.isDarkModeActive = false;

            // Menu position
            this.currentMenuPosition = 'bottom-right';

            // Language preference
            const savedLanguage = localStorage.getItem('map_current_language');
            this.currentLanguage = savedLanguage || 'en';

            // Translation data - will be loaded dynamically
            this.translations = {};
            this.translationsLoaded = false;
            this.translationLoadPromises = {};

            // Initialize translations loading with the correct language
            this.initializeTranslations();

            // Modal system properties
            this.currentView = 'main-menu';
            this.previousView = null;
            this.isTransitioning = false;

            this.init();
        }

        /**
         * Initialize the plugin
         */
        init() {
            // Speech synthesis is optional - widget should work without it
            if (!this.speechSynthesis) {
                // Text-to-speech features will be disabled
            }

            this.bindEvents();
            this.setupKeyboardShortcuts();
            this.loadSettings();
        }

        /**
         * Bind event handlers
         */
        bindEvents() {
            const self = this;

            // Main toggle button
            $(document).on('click.mapAccessibility', '#map-main-toggle', function(e) {
                e.preventDefault();
                self.toggleWidget();
            });

            // Close panel button
            $(document).on('click.mapAccessibility', '#map-close-panel', function(e) {
                e.preventDefault();
                self.closeWidget();
            });

            // TTS toggle button
            $(document).on('click.mapAccessibility', '#map-tts-toggle', function(e) {
                e.preventDefault();
                self.toggleTextSelection();
            });

            // Dyslexic Font toggle button
            $(document).on('click.mapAccessibility', '#map-dyslexic-font-toggle', function(e) {
                e.preventDefault();
                self.toggleDyslexicFont();
            });

            // Reading Guide toggle button
            $(document).on('click.mapAccessibility', '#map-reading-guide-toggle', function(e) {
                e.preventDefault();
                self.toggleReadingGuide();
            });

            // Font Size toggle button
            $(document).on('click.mapAccessibility', '#map-font-size-toggle', function(e) {
                e.preventDefault();
                e.stopPropagation();
                self.toggleFontSizeControls();
            });

            // Line Spacing toggle button
            $(document).on('click.mapAccessibility', '#map-line-spacing-toggle', function(e) {
                e.preventDefault();
                e.stopPropagation();
                self.toggleLineSpacingControls();
            });

            // Font Size control buttons
            $(document).on('click.mapAccessibility', '#map-font-size-increase', function(e) {
                e.preventDefault();
                self.increaseFontSize();
            });

            $(document).on('click.mapAccessibility', '#map-font-size-decrease', function(e) {
                e.preventDefault();
                self.decreaseFontSize();
            });

            $(document).on('click.mapAccessibility', '#map-font-size-reset', function(e) {
                e.preventDefault();
                self.resetFontSize();
            });

            // Category Reset button
            $(document).on('click.mapAccessibility', '#map-reset-category', function(e) {
                e.preventDefault();
                // Reset to main menu (this method will be added by modal-system.js)
                if (typeof self.resetCurrentCategory === 'function') {
                    self.resetCurrentCategory();
                } else {
                    console.warn('resetCurrentCategory method not available yet');
                }
            });

            // Additional event bindings will be added by other modules
        }

        /**
         * Toggle navigation arrow rotation for expandable sections
         * @param {jQuery} $toggle - The toggle button element
         */
        toggleNavigationArrow($toggle) {
            // Use a small delay to ensure the data-active attribute is updated first
            setTimeout(() => {
                const $arrow = $toggle.find('.map-category-arrow');
                if ($arrow.length) {
                    const isActive = $toggle.attr('data-active') === 'true';

                    if (isActive) {
                        // Rotate to down arrow (∨)
                        $arrow.css('transform', 'rotate(90deg)');
                    } else {
                        // Reset to right arrow (>)
                        $arrow.css('transform', 'rotate(0deg)');
                    }
                }
            }, 10);
        }

        /**
         * Toggle reset button visibility based on color value
         * @param {jQuery} $colorPicker - The color picker element
         */
        toggleResetButtonVisibility($colorPicker) {
            const colorValue = $colorPicker.val();
            const pickerId = $colorPicker.attr('id');
            const $resetBtn = $(`[data-target="${pickerId}"]`);

            if ($resetBtn.length) {
                // Show reset button if color has been changed from default (empty or #000000)
                if (colorValue && colorValue !== '#000000' && colorValue !== '') {
                    $resetBtn.addClass('visible').show();
                } else {
                    $resetBtn.removeClass('visible').hide();
                }
            }
        }



        // Additional core methods will be added here
    }

    // Export the class for use by other modules
    window.MapAccessibility = MapAccessibility;

})(jQuery);
