/**
 * Text Formatting Module - Font and Text Appearance
 *
 * This module contains:
 * - Font size controls and management
 * - Line spacing controls
 * - Dyslexic font toggle
 * - Reading guide functionality
 * - Text formatting state management
 *
 * @package MyAccessibilityPlugin
 * @since 1.0.0
 */

(function($) {
    'use strict';

    // Extend MapAccessibility with text formatting event handlers
    if (typeof window.MapAccessibility !== 'undefined') {

        // Add reading & cognitive support event bindings
        const originalBindEvents = window.MapAccessibility.prototype.bindEvents;
        window.MapAccessibility.prototype.bindEvents = function() {
            originalBindEvents.call(this);

            const self = this;

            // ADHD Focus Mode toggle
            $(document).on('click.mapAccessibility', '#map-nav-adhd-focus-toggle', function(e) {
                e.preventDefault();
                self.toggleADHDFocusMode();
            });

            // Big Cursor toggle
            $(document).on('click.mapAccessibility', '#map-big-cursor-toggle', function(e) {
                e.preventDefault();
                self.toggleBigCursor();
            });


        };

        /**
         * Toggle dyslexic font
         */
        window.MapAccessibility.prototype.toggleDyslexicFont = function() {
            const $toggle = $('#map-dyslexic-font-toggle');
            const isActive = $toggle.attr('data-active') === 'true';

            if (isActive) {
                $toggle.attr('data-active', 'false');
                $toggle.removeClass('active');
                this.disableDyslexicFont();
                this.isDyslexicFontActive = false;
            } else {
                $toggle.attr('data-active', 'true');
                $toggle.addClass('active');
                this.enableDyslexicFont();
                this.isDyslexicFontActive = true;
            }

            this.saveUserPreferences();
        };

        /**
         * Enable dyslexic font
         */
        window.MapAccessibility.prototype.enableDyslexicFont = function() {
            $('body').addClass('dyslexic-font');
            $('body').addClass('dyslexic-font-loading');

            setTimeout(() => {
                $('body').removeClass('dyslexic-font-loading');
            }, 500);
        };

        /**
         * Disable dyslexic font
         */
        window.MapAccessibility.prototype.disableDyslexicFont = function() {
            $('body').removeClass('dyslexic-font dyslexic-font-loading');
        };

        /**
         * Restore dyslexic font state from localStorage
         */
        window.MapAccessibility.prototype.restoreDyslexicFontState = function() {
            const $toggle = $('#map-dyslexic-font-toggle');
            if ($toggle.length) {
                $toggle.attr('data-active', 'true');
                $toggle.addClass('active');
                this.enableDyslexicFont();
            }
        };

        /**
         * Toggle reading guide
         */
        window.MapAccessibility.prototype.toggleReadingGuide = function() {
            const $toggle = $('#map-reading-guide-toggle');
            const isActive = $toggle.attr('data-active') === 'true';

            if (isActive) {
                $toggle.attr('data-active', 'false');
                $toggle.removeClass('active');
                this.disableReadingGuide();
                this.isReadingGuideActive = false;
            } else {
                $toggle.attr('data-active', 'true');
                $toggle.addClass('active');
                this.enableReadingGuide();
                this.isReadingGuideActive = true;
            }

            this.saveUserPreferences();
        };

        /**
         * Enable reading guide
         */
        window.MapAccessibility.prototype.enableReadingGuide = function() {
            $('#map-reading-guide-line').remove();
            $(document).off('mousemove.mapReadingGuide');

            $('body').append('<div id="map-reading-guide-line" class="map-reading-guide hidden"></div>');

            setTimeout(() => {
                $(document).on('mousemove.mapReadingGuide', (e) => {
                    this.updateReadingGuidePosition(e.clientY);
                });

                const $guideLine = $('#map-reading-guide-line');
                if ($guideLine.length) {
                    $guideLine.removeClass('hidden');
                }
            }, 100);
        };

        /**
         * Disable reading guide
         */
        window.MapAccessibility.prototype.disableReadingGuide = function() {
            $(document).off('mousemove.mapReadingGuide');

            const $guideLine = $('#map-reading-guide-line');
            if ($guideLine.length) {
                $guideLine.addClass('hidden');
                setTimeout(() => {
                    $guideLine.remove();
                }, 100);
            }
        };

        /**
         * Update reading guide position
         */
        window.MapAccessibility.prototype.updateReadingGuidePosition = function(mouseY) {
            const $guideLine = $('#map-reading-guide-line');
            if ($guideLine.length && this.isReadingGuideActive) {
                $guideLine.css('top', mouseY + 'px');

                if (!$guideLine.hasClass('smooth')) {
                    $guideLine.addClass('smooth');
                }
            }
        };

        /**
         * Restore reading guide state from localStorage
         */
        window.MapAccessibility.prototype.restoreReadingGuideState = function() {
            setTimeout(() => {
                this.enableReadingGuide();

                const $toggle = $('#map-reading-guide-toggle');
                if ($toggle.length) {
                    $toggle.attr('data-active', 'true');
                    $toggle.addClass('active');
                } else {
                    setTimeout(() => {
                        const $toggleDelayed = $('#map-reading-guide-toggle');
                        if ($toggleDelayed.length) {
                            $toggleDelayed.attr('data-active', 'true');
                            $toggleDelayed.addClass('active');
                        }
                    }, 1000);
                }
            }, 200);
        };

        /**
         * Toggle font size controls visibility
         */
        window.MapAccessibility.prototype.toggleFontSizeControls = function() {
            const $toggle = $('#map-font-size-toggle');
            const $controls = $('#map-font-size-controls');
            const isActive = $toggle.attr('data-active') === 'true';

            if (isActive) {
                $controls.slideUp(200);
                $toggle.attr('data-active', 'false');
                $toggle.removeClass('active');
                this.toggleNavigationArrow($toggle);
            } else {
                $controls.slideDown(200);
                $toggle.attr('data-active', 'true');
                $toggle.addClass('active');
                this.toggleNavigationArrow($toggle);
            }
        };

        /**
         * Toggle line spacing controls visibility
         */
        window.MapAccessibility.prototype.toggleLineSpacingControls = function() {
            const $toggle = $('#map-line-spacing-toggle');
            const $controls = $('#map-line-spacing-controls');
            const isActive = $toggle.attr('data-active') === 'true';

            if (isActive) {
                $controls.slideUp(200);
                $toggle.attr('data-active', 'false');
                $toggle.removeClass('active');
                this.toggleNavigationArrow($toggle);
            } else {
                $controls.slideDown(200);
                $toggle.attr('data-active', 'true');
                $toggle.addClass('active');
                this.toggleNavigationArrow($toggle);
            }
        };





        /**
         * Toggle ADHD Focus Mode
         */
        window.MapAccessibility.prototype.toggleADHDFocusMode = function() {
            const $toggle = $('#map-nav-adhd-focus-toggle');
            const isActive = $toggle.attr('data-active') === 'true';

            if (isActive) {
                $toggle.attr('data-active', 'false').removeClass('active');
                $('body').removeClass('map-adhd-focus-mode');
                $('#map-adhd-focus-overlay, #map-adhd-focus-border').hide();
                $(document).off('mousemove.adhd');
                this.adhdFocusMode = false;
            } else {
                $toggle.attr('data-active', 'true').addClass('active');
                $('body').addClass('map-adhd-focus-mode');
                this.adhdFocusMode = true;
                this.initializeADHDFocusMode();
            }

            this.saveUserPreferences();
        };

        /**
         * Initialize ADHD Focus Mode functionality
         */
        window.MapAccessibility.prototype.initializeADHDFocusMode = function() {
            $('#map-adhd-focus-overlay, #map-adhd-focus-border').remove();

            $('body').append(`
                <div id="map-adhd-focus-overlay" style="
                    position: fixed;
                    top: 0;
                    left: 0;
                    width: 100%;
                    height: 100%;
                    background: rgba(0, 0, 0, 0.69);
                    z-index: 2147483647;
                    pointer-events: none;
                    display: none;
                    clip-path: polygon(0% 0%, 0% 100%, 100% 100%, 100% 0%);
                "></div>
            `);

            const isDarkMode = $('.map-accessibility-widget').hasClass('map-dark-mode');
            const borderColor = isDarkMode ? '#7c3aed' : '#6366f1';
            const glowColor = isDarkMode ? '124, 58, 237' : '99, 102, 241';

            $('body').append(`
                <div id="map-adhd-focus-border" style="
                    position: fixed;
                    left: 0;
                    width: 100%;
                    height: 125px;
                    border-top: 3px solid ${borderColor};
                    border-bottom: 3px solid ${borderColor};
                    border-left: none;
                    border-right: none;
                    box-shadow:
                        0 -15px 30px rgba(${glowColor}, 0.6),
                        0 15px 30px rgba(${glowColor}, 0.6),
                        0 -25px 50px rgba(${glowColor}, 0.4),
                        0 25px 50px rgba(${glowColor}, 0.4),
                        0 -35px 70px rgba(${glowColor}, 0.25),
                        0 35px 70px rgba(${glowColor}, 0.25),
                        inset 0 0 0 1px rgba(255, 255, 255, 0.1);
                    z-index: 2147483648;
                    pointer-events: none;
                    display: none;
                    background: transparent;
                    transition: none;
                "></div>
            `);

            $(document).off('mousemove.adhd').on('mousemove.adhd', (e) => {
                if (this.adhdFocusMode) {
                    this.updateADHDFocusSpotlight(e.clientX, e.clientY);
                }
            });

            $('#map-adhd-focus-overlay, #map-adhd-focus-border').show();
        };

        /**
         * Update ADHD Focus colors when dark mode changes
         */
        window.MapAccessibility.prototype.updateADHDFocusColors = function() {
            const $border = $('#map-adhd-focus-border');
            if ($border.length && this.adhdFocusMode) {
                const isDarkMode = $('.map-accessibility-widget').hasClass('map-dark-mode');
                const borderColor = isDarkMode ? '#7c3aed' : '#6366f1';
                const glowColor = isDarkMode ? '124, 58, 237' : '99, 102, 241';

                $border.css({
                    'border-top': `3px solid ${borderColor}`,
                    'border-bottom': `3px solid ${borderColor}`,
                    'border-top-color': borderColor,
                    'border-bottom-color': borderColor,
                    'box-shadow': `
                        0 -15px 30px rgba(${glowColor}, 0.6),
                        0 15px 30px rgba(${glowColor}, 0.6),
                        0 -25px 50px rgba(${glowColor}, 0.4),
                        0 25px 50px rgba(${glowColor}, 0.4),
                        0 -35px 70px rgba(${glowColor}, 0.25),
                        0 35px 70px rgba(${glowColor}, 0.25),
                        inset 0 0 0 1px rgba(255, 255, 255, 0.1)
                    `
                });
            }
        };

        /**
         * Update ADHD Focus Mode spotlight position
         */
        window.MapAccessibility.prototype.updateADHDFocusSpotlight = function(_, mouseY) {
            const $overlay = $('#map-adhd-focus-overlay');
            const $border = $('#map-adhd-focus-border');
            const spotlightHeight = 125;
            const windowHeight = window.innerHeight;

            const focusTop = Math.max(0, mouseY - spotlightHeight / 2);
            const focusBottom = Math.min(windowHeight, mouseY + spotlightHeight / 2);

            const topPercent = (focusTop / windowHeight) * 100;
            const bottomPercent = (focusBottom / windowHeight) * 100;

            const clipPath = `polygon(
                0% 0%,
                0% ${topPercent}%,
                100% ${topPercent}%,
                100% 0%,
                0% 0%,
                0% ${bottomPercent}%,
                100% ${bottomPercent}%,
                100% 100%,
                0% 100%,
                0% ${bottomPercent}%
            )`;

            $overlay.css('clip-path', clipPath);

            const borderWidth = 3;
            $border.css({
                'top': (focusTop - borderWidth) + 'px',
                'height': (focusBottom - focusTop) + 'px'
            });
        };

        /**
         * Toggle Big Cursor Mode
         */
        window.MapAccessibility.prototype.toggleBigCursor = function() {
            const $toggle = $('#map-big-cursor-toggle');
            const isActive = $toggle.attr('data-active') === 'true';

            if (isActive) {
                $toggle.attr('data-active', 'false').removeClass('active');
                $('body').removeClass('map-big-cursor-mode');
                this.bigCursorMode = false;
            } else {
                $toggle.attr('data-active', 'true').addClass('active');
                $('body').addClass('map-big-cursor-mode');
                this.bigCursorMode = true;
            }

            this.saveUserPreferences();
        };

        // Reading & cognitive support methods will be added here
    }

})(jQuery);
