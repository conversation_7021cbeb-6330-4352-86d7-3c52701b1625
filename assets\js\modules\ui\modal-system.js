/**
 * Modal System Module - Widget Panel and Navigation
 *
 * This module contains:
 * - Widget opening/closing functionality
 * - Modal view transitions and animations
 * - Main menu and category navigation
 * - Focus management
 * - View state management
 *
 * @package MyAccessibilityPlugin
 * @since 1.0.0
 */

(function($) {
    'use strict';

    // Extend MapAccessibility with modal system functionality
    if (typeof window.MapAccessibility !== 'undefined') {

        /**
         * Toggle widget visibility
         */
        window.MapAccessibility.prototype.toggleWidget = function() {
            const $toggle = $('#map-main-toggle');
            const isExpanded = $toggle.attr('aria-expanded') === 'true';

            if (isExpanded) {
                this.closeWidget();
            } else {
                this.openWidget();
            }
        };

        /**
         * Open widget panel with professional button fade
         */
        window.MapAccessibility.prototype.openWidget = function() {
            const $toggle = $('#map-main-toggle');
            const $panel = $('#map-widget-panel');
            const $widget = $('#map-accessibility-widget');

            this.isTransitioning = false;

            $toggle.attr('aria-expanded', 'true');
            $panel.show().attr('aria-hidden', 'false');
            $widget.addClass('map-panel-active');
            $toggle.addClass('map-panel-open');

            $('.map-modal-view').removeClass('map-modal-view-active');
            $('#map-main-menu').addClass('map-modal-view-active');
            this.currentView = 'main-menu';
            this.currentCategory = null;

            $('#map-panel-title').show();
            $('#map-header-navigation').hide();
            this.hideResetButton();

            setTimeout(() => {
                $toggle.removeClass('map-panel-open').addClass('map-fading-out');
            }, 100);

            setTimeout(() => {
                this.ensureMainMenuVisible();
            }, 50);

            setTimeout(() => {
                $panel.find('button, input, select').first().focus();
            }, 200);
        };

        /**
         * Ensure main menu is visible and properly set up
         */
        window.MapAccessibility.prototype.ensureMainMenuVisible = function() {
            const $mainMenu = $('#map-main-menu');
            const $allViews = $('.map-modal-view');

            $allViews.removeClass('map-modal-view-active').hide();
            $mainMenu.addClass('map-modal-view-active').show();
            this.currentView = 'main-menu';
            this.currentCategory = null;
            $('#map-panel-title').show();
            $('#map-header-navigation').hide();
            this.hideResetButton();
        };

        /**
         * Close widget panel and restore button
         */
        window.MapAccessibility.prototype.closeWidget = function() {
            const $toggle = $('#map-main-toggle');
            const $panel = $('#map-widget-panel');
            const $widget = $('#map-accessibility-widget');

            this.isTransitioning = false;
            $toggle.removeClass('map-fading-out map-panel-open');
            $widget.removeClass('map-panel-active');
            $toggle.attr('aria-expanded', 'false');
            $panel.hide().attr('aria-hidden', 'true');
            $('.map-modal-view').removeClass('map-modal-view-active');
            $('#map-main-menu').addClass('map-modal-view-active');
            this.currentView = 'main-menu';
            this.currentCategory = null;
            $('#map-panel-title').show();
            $('#map-header-navigation').hide();
            this.hideResetButton();
            $toggle.focus();
        };

        /**
         * Show main menu (Level 1) with premium animation
         */
        window.MapAccessibility.prototype.showMainMenu = function() {
            if (this.isTransitioning) return;

            this.previousView = this.currentView;
            this.currentView = 'main-menu';
            this.currentCategory = null;
            this.hideResetButton();
            $('#map-panel-title').show();
            $('#map-header-navigation').hide();
            this.switchView('map-main-menu');
        };

        /**
         * Show specific section (Level 2)
         * @param {string} section - The section to show (text, colors, navigation, preferences)
         */
        window.MapAccessibility.prototype.showSection = function(section) {
            if (this.isTransitioning) return;

            const validSections = ['text', 'colors', 'navigation', 'preferences'];
            if (!validSections.includes(section)) {
                return;
            }

            this.previousView = this.currentView;
            this.currentView = section;
            this.currentCategory = section;
            $('#map-panel-title').hide();
            $('#map-header-navigation').show();

            const viewId = `map-view-${section}`;
            const titleKey = this.getCategoryTitleKey(viewId);
            const categoryTitle = titleKey ? this.translate(titleKey) : section.charAt(0).toUpperCase() + section.slice(1);
            $('#map-header-category-title').text(categoryTitle);
            this.showResetButton();

            if (section === 'colors') {
                setTimeout(() => {
                    this.initializeContrastThemesSection();
                    this.initializeCustomThemeColorsSection();
                }, 100);
            } else if (section === 'navigation') {
                setTimeout(() => {
                    this.initializeNavigationSection();
                }, 100);
            } else if (section === 'preferences') {
                setTimeout(() => {
                    this.initializePreferencesSection();
                }, 100);
            }

            this.switchView(`map-view-${section}`);
        };

        /**
         * Switch between modal views with premium smooth transitions
         * @param {string} targetViewId - The ID of the target view element
         */
        window.MapAccessibility.prototype.switchView = function(targetViewId) {
            if (this.isTransitioning) return;

            this.isTransitioning = true;
            const $currentView = $('.map-modal-view-active');
            const $targetView = $(`#${targetViewId}`);

            if ($targetView.length === 0) {
                this.isTransitioning = false;
                return;
            }

            const isForward = this.isNavigatingForward(targetViewId);
            const animationDuration = 450;

            this.performPremiumTransition($currentView, $targetView, isForward, animationDuration);
        };

        /**
         * Determine if navigation is forward (category → options) or backward (options → category)
         * @param {string} targetViewId - The target view ID
         * @returns {boolean} True if navigating forward
         */
        window.MapAccessibility.prototype.isNavigatingForward = function(targetViewId) {
            const currentViewId = $('.map-modal-view-active').attr('id');

            if (currentViewId === 'map-main-menu' && targetViewId.startsWith('map-view-')) {
                return true;
            }

            if (currentViewId && currentViewId.startsWith('map-view-') && targetViewId === 'map-main-menu') {
                return false;
            }

            return true;
        };

        /**
         * Perform premium transition animation between views
         * @param {jQuery} $currentView - Current active view
         * @param {jQuery} $targetView - Target view to show
         * @param {boolean} isForward - Direction of navigation
         * @param {number} duration - Animation duration in ms
         */
        window.MapAccessibility.prototype.performPremiumTransition = function($currentView, $targetView, isForward, duration) {
            const forwardClass = isForward ? 'forward' : 'backward';

            $targetView.removeClass('map-modal-view-active')
                      .addClass(`map-view-entering-${forwardClass}`)
                      .show();

            if ($currentView.length > 0) {
                $currentView.addClass(`map-view-exiting-${forwardClass}`);
            }

            setTimeout(() => {
                if ($currentView.length > 0) {
                    $currentView.removeClass('map-modal-view-active')
                              .removeClass(`map-view-exiting-${forwardClass}`)
                              .hide();
                }

                $targetView.removeClass(`map-view-entering-${forwardClass}`)
                          .addClass('map-modal-view-active');

                this.isTransitioning = false;

                setTimeout(() => {
                    this.manageFocusAfterTransition($targetView);
                }, 100);
            }, duration);
        };

        /**
         * Manage focus after view transitions
         * @param {jQuery} $targetView - The target view element
         */
        window.MapAccessibility.prototype.manageFocusAfterTransition = function($targetView) {
            const $firstInteractive = $targetView.find('button, input, select, [tabindex]:not([tabindex="-1"])').first();
            if ($firstInteractive.length > 0) {
                $firstInteractive.focus();
            }
        };

        /**
         * Handle keyboard navigation for category buttons
         * @param {Event} e - The keyboard event
         * @param {jQuery} $currentButton - The currently focused button
         */
        window.MapAccessibility.prototype.handleCategoryKeyNavigation = function(e, $currentButton) {
            const $categoryButtons = $('.map-category-button:not(.map-category-disabled)');
            const currentIndex = $categoryButtons.index($currentButton);
            let targetIndex = currentIndex;

            switch (e.key) {
                case 'ArrowDown':
                case 'ArrowRight':
                    e.preventDefault();
                    targetIndex = (currentIndex + 1) % $categoryButtons.length;
                    break;
                case 'ArrowUp':
                case 'ArrowLeft':
                    e.preventDefault();
                    targetIndex = (currentIndex - 1 + $categoryButtons.length) % $categoryButtons.length;
                    break;
                case 'Home':
                    e.preventDefault();
                    targetIndex = 0;
                    break;
                case 'End':
                    e.preventDefault();
                    targetIndex = $categoryButtons.length - 1;
                    break;
                case 'Enter':
                case ' ':
                    e.preventDefault();
                    $currentButton.click();
                    return;
                default:
                    return;
            }

            $categoryButtons.eq(targetIndex).focus();
        };

        // Extend bindEvents to add modal system event handlers
        const originalBindEvents = window.MapAccessibility.prototype.bindEvents;
        window.MapAccessibility.prototype.bindEvents = function() {
            originalBindEvents.call(this);

            const self = this;

            // Modal system event handlers
            // Category buttons
            $(document).on('click.mapAccessibility', '.map-category-button:not(.map-category-disabled)', function(e) {
                e.preventDefault();
                const category = $(this).data('category');
                if (category) {
                    self.showSection(category);
                }
            });

            // Back buttons (including header back button)
            $(document).on('click.mapAccessibility', '.map-back-button, #map-header-back-button', function(e) {
                e.preventDefault();
                self.showMainMenu();
            });

            // Keyboard navigation for category buttons
            $(document).on('keydown.mapAccessibility', '.map-category-button', function(e) {
                self.handleCategoryKeyNavigation(e, $(this));
            });
        };

        // Additional modal system methods will be added here
    }

})(jQuery);
