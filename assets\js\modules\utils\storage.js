/**
 * Storage Module - LocalStorage and Settings Persistence
 *
 * This module contains:
 * - User preferences saving and loading
 * - LocalStorage management
 * - Settings restoration on page load
 * - State persistence utilities
 *
 * @package MyAccessibilityPlugin
 * @since 1.0.0
 */

(function($) {
    'use strict';

    // Extend MapAccessibility with storage functionality
    if (typeof window.MapAccessibility !== 'undefined') {

        /**
         * Load user settings and preferences
         */
        window.MapAccessibility.prototype.loadSettings = function() {
            if (typeof mapAjax !== 'undefined' && mapAjax.settings) {
                const settings = mapAjax.settings;

                $('#map-speed-control').val(settings.speech_rate);
                $('.map-speed-value').text(settings.speech_rate + 'x');

                this.highlightEnabled = $('#map-highlight-text').is(':checked');
                this.autoScrollEnabled = $('#map-auto-scroll').is(':checked');
            } else {
                this.highlightEnabled = false;
                this.autoScrollEnabled = false;
            }
            this.loadUserPreferences();
        };

        /**
         * Load user preferences from localStorage
         */
        window.MapAccessibility.prototype.loadUserPreferences = function() {
            try {
                const ttsActive = localStorage.getItem('map_tts_active');
                if (ttsActive === 'true') {
                    this.isTextToSpeechActive = true;
                    this.restoreTextToSpeechState();
                }
                const speechRate = localStorage.getItem('map_speech_rate');
                if (speechRate) {
                    $('#map-speed-control').val(parseFloat(speechRate));
                    $('.map-speed-value').text(speechRate + 'x');
                }

                const highlightEnabled = localStorage.getItem('map_highlight_enabled');
                if (highlightEnabled !== null) {
                    this.highlightEnabled = highlightEnabled === 'true';
                    $('#map-highlight-text').prop('checked', this.highlightEnabled);
                }

                const autoScrollEnabled = localStorage.getItem('map_autoscroll_enabled');
                if (autoScrollEnabled !== null) {
                    this.autoScrollEnabled = autoScrollEnabled === 'true';
                    $('#map-auto-scroll').prop('checked', this.autoScrollEnabled);
                }

                const dyslexicFontActive = localStorage.getItem('map_dyslexic_font_active');
                if (dyslexicFontActive === 'true') {
                    this.isDyslexicFontActive = true;
                    this.restoreDyslexicFontState();
                }

                const readingGuideActive = localStorage.getItem('map_reading_guide_active');
                if (readingGuideActive === 'true') {
                    this.isReadingGuideActive = true;
                    this.restoreReadingGuideState();
                }

                const adhdFocusModeActive = localStorage.getItem('map_adhd_focus_mode');
                if (adhdFocusModeActive === 'true') {
                    this.adhdFocusMode = true;
                    this.restoreADHDFocusModeState();
                }

                const bigCursorModeActive = localStorage.getItem('map_big_cursor_mode');
                if (bigCursorModeActive === 'true') {
                    this.bigCursorMode = true;
                    this.restoreBigCursorModeState();
                }

                const textMagnificationModeActive = localStorage.getItem('map_text_magnification_mode');
                if (textMagnificationModeActive === 'true') {
                    this.textMagnificationMode = true;
                    this.restoreTextMagnificationModeState();
                }

                // Load font size state
                const savedFontSize = localStorage.getItem('map_font_size');
                if (savedFontSize !== null) {
                    this.currentFontSize = parseInt(savedFontSize, 10);
                    this.restoreFontSizeState();
                }

                // Load line spacing state
                const savedLineSpacing = localStorage.getItem('map_line_spacing');
                if (savedLineSpacing !== null) {
                    this.currentLineSpacing = parseFloat(savedLineSpacing);
                    this.restoreLineSpacingState();
                }

                // Load letter spacing state
                const savedLetterSpacing = localStorage.getItem('map_letter_spacing');
                if (savedLetterSpacing !== null) {
                    this.currentLetterSpacing = parseFloat(savedLetterSpacing);
                    this.restoreLetterSpacingState();
                }

                // Load text alignment state
                const savedTextAlignment = localStorage.getItem('map_text_alignment');
                if (savedTextAlignment !== null) {
                    this.currentTextAlignment = savedTextAlignment;
                    this.restoreTextAlignmentState();
                }

                // Load contrast theme state
                const savedContrastTheme = localStorage.getItem('map_contrast_theme');
                if (savedContrastTheme !== null) {
                    this.currentContrastTheme = savedContrastTheme;
                }

                // Load custom theme colors and state
                const savedCustomThemeColors = localStorage.getItem('map_custom_theme_colors');
                const savedCustomThemeActive = localStorage.getItem('map_custom_theme_active');
                if (savedCustomThemeColors !== null) {
                    try {
                        this.customThemeColors = JSON.parse(savedCustomThemeColors);
                    } catch (e) {
                        // Ignore parsing errors
                    }
                }
                if (savedCustomThemeActive === 'true') {
                    this.isCustomThemeActive = true;
                    this.currentContrastTheme = 'custom';
                }

                // Load dark mode state
                const darkModeActive = localStorage.getItem('map_dark_mode_active');
                if (darkModeActive === 'true') {
                    this.isDarkModeActive = true;
                    this.restoreDarkModeState();
                }

                // Load menu position preference
                const savedMenuPosition = localStorage.getItem('map_current_menu_position');
                if (savedMenuPosition) {
                    this.currentMenuPosition = savedMenuPosition;
                    this.initializeMenuPositionState();
                }

                if (this.translationsLoaded) {
                    this.applyTranslations();
                }

                this.restoreContrastThemeState();

            } catch (e) {
                // Ignore storage errors
            }
        };

        /**
         * Save user preferences to localStorage
         */
        window.MapAccessibility.prototype.saveUserPreferences = function() {
            try {
                localStorage.setItem('map_tts_active', this.isTextToSpeechActive.toString());

                const speechRate = $('#map-speed-control').val();
                if (speechRate) {
                    localStorage.setItem('map_speech_rate', speechRate);
                }
                localStorage.setItem('map_highlight_enabled', this.highlightEnabled.toString());
                localStorage.setItem('map_autoscroll_enabled', this.autoScrollEnabled.toString());
                localStorage.setItem('map_dyslexic_font_active', this.isDyslexicFontActive.toString());
                localStorage.setItem('map_reading_guide_active', this.isReadingGuideActive.toString());
                localStorage.setItem('map_adhd_focus_mode', this.adhdFocusMode.toString());
                localStorage.setItem('map_big_cursor_mode', this.bigCursorMode.toString());
                localStorage.setItem('map_text_magnification_mode', this.textMagnificationMode.toString());
                localStorage.setItem('map_font_size', this.currentFontSize.toString());
                localStorage.setItem('map_line_spacing', this.currentLineSpacing.toString());
                localStorage.setItem('map_letter_spacing', this.currentLetterSpacing.toString());
                localStorage.setItem('map_text_alignment', this.currentTextAlignment);
                localStorage.setItem('map_contrast_theme', this.currentContrastTheme);

                localStorage.setItem('map_custom_theme_colors', JSON.stringify(this.customThemeColors));
                localStorage.setItem('map_custom_theme_active', this.isCustomThemeActive.toString());
                localStorage.setItem('map_dark_mode_active', this.isDarkModeActive.toString());
                localStorage.setItem('map_current_language', this.currentLanguage);
                localStorage.setItem('map_current_menu_position', this.currentMenuPosition);
                localStorage.setItem('map_last_view', this.currentView);

            } catch (e) {
                // Ignore storage errors
            }
        };

        /**
         * Restore text-to-speech active state
         */
        window.MapAccessibility.prototype.restoreTextToSpeechState = function() {
            const $toggle = $('#map-tts-toggle');
            if ($toggle.length) {
                $toggle.attr('data-active', 'true');
                $toggle.addClass('active');
                this.enableTextSelection();
            }
        };

        /**
         * Clear all user preferences (for reset functionality)
         */
        window.MapAccessibility.prototype.clearUserPreferences = function() {
            try {
                localStorage.removeItem('map_tts_active');
                localStorage.removeItem('map_speech_rate');
                localStorage.removeItem('map_highlight_enabled');
                localStorage.removeItem('map_autoscroll_enabled');
                localStorage.removeItem('map_dyslexic_font_active');
                localStorage.removeItem('map_reading_guide_active');
                localStorage.removeItem('map_adhd_focus_mode');
                localStorage.removeItem('map_big_cursor_mode');
                localStorage.removeItem('map_text_magnification_mode');
                localStorage.removeItem('map_font_size');
                localStorage.removeItem('map_line_spacing');
                localStorage.removeItem('map_letter_spacing');
                localStorage.removeItem('map_text_alignment');
                localStorage.removeItem('map_contrast_theme');
                localStorage.removeItem('map_custom_theme_colors');
                localStorage.removeItem('map_custom_theme_active');
                localStorage.removeItem('map_dark_mode_active');
                localStorage.removeItem('map_current_language');
                localStorage.removeItem('map_current_menu_position');
            } catch (e) {
                // Silent error handling
            }
        };

        /**
         * Destroy the accessibility instance and clean up
         */
        window.MapAccessibility.prototype.destroy = function() {
            // Stop any ongoing speech
            if (this.currentUtterance) {
                this.speechSynthesis.cancel();
                this.currentUtterance = null;
            }

            // Remove event listeners
            $(document).off('.mapTTS');
            $(document).off('.mapAccessibility');
            $(window).off('.mapAccessibility');

            // Remove floating button if it exists
            $('#map-floating-play-button').remove();

            // Clear any timeouts/intervals
            this.clearHighlight();

            // Reset flags
            this.isPlaying = false;
            this.isPaused = false;
            this.isSpeechInProgress = false;
            this.isTextToSpeechActive = false;
            this.isDyslexicFontActive = false;
            this.isReadingGuideActive = false;
            this.currentFontSize = 100;
            this.currentLineSpacing = 1.5;
            this.currentText = '';
            this.currentPosition = 0;
        };

        // Additional storage methods will be added here
    }

})(jQuery);
