/**
 * Translations Module - Internationalization and Language Support
 *
 * This module contains:
 * - Translation loading and management
 * - Language file fetching
 * - Translation application to UI elements
 * - Multi-language support utilities
 *
 * @package MyAccessibilityPlugin
 * @since 1.0.0
 */

(function($) {
    'use strict';

    // Extend MapAccessibility with translation methods
    if (typeof window.MapAccessibility !== 'undefined') {

        /**
         * Initialize translations by loading the default language
         */
        window.MapAccessibility.prototype.initializeTranslations = async function() {
            try {
                await this.loadLanguage(this.currentLanguage);
                this.translationsLoaded = true;

                // Initialize language state after DOM is ready
                $(document).ready(() => {
                    this.initializeLanguageState();
                    this.initializeMenuPositionState();
                });
            } catch (error) {
                // Fallback to basic English translations if loading fails
                this.translations = {
                    en: {
                        'Accessibility Tools': 'Accessibility Tools',
                        'Language changed to English!': 'Language changed to English!',
                        'Language changed to Français!': 'Language changed to Français!',
                        'Language preference updated!': 'Language preference updated!'
                    }
                };
                this.translationsLoaded = true;

                // Initialize language state even with fallback translations
                $(document).ready(() => {
                    this.initializeLanguageState();
                    this.initializeMenuPositionState();
                });
            }
        };

        /**
         * Load translation file for a specific language
         * @param {string} langCode - Language code (e.g., 'en', 'fr')
         * @returns {Promise} Promise that resolves when translations are loaded
         */
        window.MapAccessibility.prototype.loadLanguage = async function(langCode) {
            // Return existing promise if already loading
            if (this.translationLoadPromises[langCode]) {
                return this.translationLoadPromises[langCode];
            }

            // Return immediately if already loaded
            if (this.translations[langCode]) {
                return Promise.resolve();
            }

            // Create new loading promise
            this.translationLoadPromises[langCode] = new Promise(async (resolve, reject) => {
                try {
                    const pluginUrl = mapAjax.plugin_url || '/wp-content/plugins/my-accessibility-plugin/';
                    const response = await fetch(`${pluginUrl}languages/${langCode}.json`);

                    if (!response.ok) {
                        throw new Error(`HTTP error! status: ${response.status}`);
                    }

                    const translations = await response.json();

                    // Flatten the nested JSON structure to match the old format
                    const flatTranslations = {};
                    Object.values(translations).forEach(category => {
                        Object.assign(flatTranslations, category);
                    });

                    this.translations[langCode] = flatTranslations;
                    resolve();
                } catch (error) {
                    reject(error);
                } finally {
                    // Clean up the promise
                    delete this.translationLoadPromises[langCode];
                }
            });

            return this.translationLoadPromises[langCode];
        };

        /**
         * Get category title translation key by view ID
         */
        window.MapAccessibility.prototype.getCategoryTitleKey = function(viewId) {
            const categoryTitleMap = {
                'map-view-text': 'Reading & Cognitive Support',
                'map-view-navigation': 'Text & Typography',
                'map-view-colors': 'Visual Adjustments',
                'map-view-preferences': 'Settings'
            };
            return categoryTitleMap[viewId] || '';
        };

        /**
         * Get translated text with fallback handling
         */
        window.MapAccessibility.prototype.translate = function(key) {
            if (!this.translationsLoaded) {
                return key;
            }

            if (this.translations[this.currentLanguage] && this.translations[this.currentLanguage][key]) {
                return this.translations[this.currentLanguage][key];
            }

            if (this.translations.en && this.translations.en[key]) {
                return this.translations.en[key];
            }

            return key;
        };

        /**
         * Apply translations to all interface elements
         */
        window.MapAccessibility.prototype.applyTranslations = function() {
            if (!this.translationsLoaded) {
                return;
            }

            $('#map-panel-title').text(this.translate('Accessibility Tools'));

            $('.map-category-item[data-category="text"] .map-category-title').text(this.translate('Reading & Cognitive Support'));
            $('.map-category-item[data-category="navigation"] .map-category-title').text(this.translate('Text & Typography'));
            $('.map-category-item[data-category="colors"] .map-category-title').text(this.translate('Visual Adjustments'));
            $('.map-category-item[data-category="preferences"] .map-category-title').text(this.translate('Settings'));

            // View titles (data-title attributes)
            $('#map-view-text').attr('data-title', this.translate('Reading & Cognitive Support'));
            $('#map-view-navigation').attr('data-title', this.translate('Text & Typography'));
            $('#map-view-colors').attr('data-title', this.translate('Visual Adjustments'));
            $('#map-view-preferences').attr('data-title', this.translate('Settings'));

            // Update the currently displayed header title if we're inside a category view
            const $headerCategoryTitle = $('#map-header-category-title');
            if ($headerCategoryTitle.is(':visible')) {
                const $activeView = $('.map-modal-view-active');
                if ($activeView.length) {
                    const viewId = $activeView.attr('id');
                    const titleKey = this.getCategoryTitleKey(viewId);
                    if (titleKey) {
                        const translatedTitle = this.translate(titleKey);
                        $headerCategoryTitle.text(translatedTitle);
                    }
                }
            }

            // Feature titles
            $('#map-tts-toggle .map-feature-title').text(this.translate('Text-to-Speech'));
            $('#map-dyslexic-font-toggle .map-feature-title').text(this.translate('Dyslexic Font'));
            $('#map-reading-guide-toggle .map-feature-title').text(this.translate('Reading Guide'));
            $('#map-font-size-toggle .map-feature-title').text(this.translate('Font Size'));
            $('#map-line-spacing-toggle .map-feature-title').text(this.translate('Line Spacing'));
            $('#map-nav-adhd-focus-toggle .map-feature-title').text(this.translate('ADHD Focus Mode'));
            $('#map-big-cursor-toggle .map-feature-title').text(this.translate('Big Cursor'));
            $('#map-text-magnification-toggle .map-feature-title').text(this.translate('Text Magnification'));
            $('#map-contrast-themes-toggle .map-feature-title').text(this.translate('Visual Themes'));
            $('#map-custom-theme-toggle .map-feature-title').text(this.translate('Color Studio'));
            $('#map-dark-mode-toggle .map-feature-title').text(this.translate('Dark mode'));
            $('#map-language-toggle .map-feature-title').text(this.translate('Language'));
            $('#map-menu-position-toggle .map-feature-title').text(this.translate('Menu Position'));
            $('#map-letter-spacing-toggle .map-feature-title').text(this.translate('Letter Spacing'));
            $('#map-text-alignment-toggle .map-feature-title').text(this.translate('Text Alignment'));
            // Feature descriptions
            $('#map-tts-toggle .map-feature-description').text(this.translate('Listen to any text on the website with natural voice synthesis'));
            $('#map-dyslexic-font-toggle .map-feature-description').text(this.translate('Switch to a font designed for better readability'));
            $('#map-reading-guide-toggle .map-feature-description').text(this.translate('Follow a reading line that moves with your cursor'));
            $('#map-font-size-toggle .map-feature-desc').text(this.translate('Adjust text size for better readability'));
            $('#map-line-spacing-toggle .map-feature-desc').text(this.translate('Increase space between lines for easier reading'));
            $('#map-nav-adhd-focus-toggle .map-feature-description').text(this.translate('Highlight content and reduce distractions'));
            $('#map-big-cursor-toggle .map-feature-description').text(this.translate('Increase cursor size for better visibility'));
            $('#map-text-magnification-toggle .map-feature-description').text(this.translate('Magnify text when hovering over it'));
            $('#map-contrast-themes-toggle .map-feature-description').text(this.translate('Choose from high contrast themes for better visibility'));
            $('#map-custom-theme-toggle .map-feature-description').text(this.translate('Design your perfect color palette with live preview'));
            $('#map-dark-mode-toggle .map-feature-description').text(this.translate('Switch to a dark theme for the accessibility menu interface'));
            $('#map-language-toggle .map-feature-description').text(this.translate('Choose your preferred interface language'));
            $('#map-menu-position-toggle .map-feature-desc').text(this.translate('Choose where the accessibility menu button appears on screen'));
            $('#map-letter-spacing-toggle .map-feature-desc').text(this.translate('Adjust space between letters for better readability'));
            $('#map-text-alignment-toggle .map-feature-desc').text(this.translate('Change text alignment for better reading experience'));
            // Language option labels
            $('.map-language-option[data-language="en"] .map-language-name').text(this.translate('English'));
            $('.map-language-option[data-language="fr"] .map-language-name').text(this.translate('French'));
            $('.map-language-option[data-language="es"] .map-language-name').text(this.translate('Spanish'));
            $('.map-language-option[data-language="de"] .map-language-name').text(this.translate('German'));
            $('.map-language-option[data-language="it"] .map-language-name').text(this.translate('Italian'));
            $('.map-language-option[data-language="pt"] .map-language-name').text(this.translate('Portuguese'));
            $('.map-language-option[data-language="ar"] .map-language-name').text(this.translate('Arabic'));
            $('.map-language-option[data-language="zh"] .map-language-name').text(this.translate('Chinese'));
            $('.map-language-option[data-language="ru"] .map-language-name').text(this.translate('Russian'));

            // Language native names (the small text under language names)
            $('.map-language-option[data-language="en"] .map-language-native').text('English');
            $('.map-language-option[data-language="fr"] .map-language-native').text('Français');
            $('.map-language-option[data-language="es"] .map-language-native').text('Español');
            $('.map-language-option[data-language="de"] .map-language-native').text('Deutsch');
            $('.map-language-option[data-language="it"] .map-language-native').text('Italiano');
            $('.map-language-option[data-language="pt"] .map-language-native').text('Português');
            $('.map-language-option[data-language="ar"] .map-language-native').text('العربية');
            $('.map-language-option[data-language="zh"] .map-language-native').text('中文');
            $('.map-language-option[data-language="ru"] .map-language-native').text('Русский');

            // Feature descriptions that were missed
            $('#map-dark-mode-toggle .map-feature-desc').text(this.translate('Switch to a dark theme for the accessibility menu interface'));
            $('#map-language-toggle .map-feature-desc').text(this.translate('Choose your preferred interface language'));
            $('#map-custom-theme-toggle .map-feature-desc').text(this.translate('Design your perfect color palette with live preview'));

            // Category descriptions
            $('#map-category-text-desc').text(this.translate('Tools to improve focus, clarity, and cognitive ease.'));
            $('#map-category-colors-desc').text(this.translate('Change colors, contrast, and themes to reduce eye strain.'));
            $('#map-category-navigation-desc').text(this.translate('Tweak text style to enhance readability and accessibility.'));
            $('#map-category-preferences-desc').text(this.translate('Manage language, layout, and menu behavior.'));

            // Menu position option labels
            $('#map-menu-position-content .map-language-option[data-position="top-left"] .map-language-name').text(this.translate('Top Left'));
            $('#map-menu-position-content .map-language-option[data-position="top-right"] .map-language-name').text(this.translate('Top Right'));
            $('#map-menu-position-content .map-language-option[data-position="bottom-left"] .map-language-name').text(this.translate('Bottom Left'));
            $('#map-menu-position-content .map-language-option[data-position="bottom-right"] .map-language-name').text(this.translate('Bottom Right'));

            // Color Studio elements - use correct parent-child relationship
            $('#map-custom-text-color').closest('.map-color-row').find('.map-color-title').text(this.translate('Text'));
            $('#map-custom-text-color').closest('.map-color-row').find('.map-color-desc').text(this.translate('Body text & paragraphs'));
            $('#map-custom-bg-color').closest('.map-color-row').find('.map-color-title').text(this.translate('Background'));
            $('#map-custom-bg-color').closest('.map-color-row').find('.map-color-desc').text(this.translate('Page & content areas'));
            $('#map-custom-link-color').closest('.map-color-row').find('.map-color-title').text(this.translate('Links'));
            $('#map-custom-link-color').closest('.map-color-row').find('.map-color-desc').text(this.translate('Hyperlinks & buttons'));
            $('#map-custom-heading-color').closest('.map-color-row').find('.map-color-title').text(this.translate('Headings'));
            $('#map-custom-heading-color').closest('.map-color-row').find('.map-color-desc').text(this.translate('Titles & headers'));

            // Navigation elements
            $('#map-header-back-button').attr('aria-label', this.translate('Back to main menu'));

            // Main category titles in the main menu
            $('#map-category-text .map-category-title').text(this.translate('Reading & Cognitive Support'));
            $('#map-category-colors .map-category-title').text(this.translate('Visual Adjustments'));
            $('#map-category-navigation .map-category-title').text(this.translate('Text & Typography'));
            $('#map-category-preferences .map-category-title').text(this.translate('Settings'));

            // Feature descriptions (more detailed ones)
            $('#map-tts-toggle .map-feature-desc').text(this.translate('Select text to hear it read aloud'));
            $('#map-dyslexic-font-toggle .map-feature-desc').text(this.translate('Apply dyslexia-friendly font to improve readability'));
            $('#map-reading-guide-toggle .map-feature-desc').text(this.translate('Show a horizontal line that follows your mouse to help focus on text'));
            $('#map-nav-adhd-focus-toggle .map-feature-desc').text(this.translate('Reduce distractions and highlight content for better focus'));
            $('#map-big-cursor-toggle .map-feature-desc').text(this.translate('Enlarge cursor size for better visibility and easier tracking'));
            $('#map-text-magnification-toggle .map-feature-desc').text(this.translate('Magnify text on hover for better readability and visibility'));
            $('#map-contrast-themes-toggle .map-feature-desc').text(this.translate('Choose a visual style that works best for you'));

            // Reset buttons - only translate the span text, not the entire button
            $('.map-reset-button span').text(this.translate('Reset'));

            // Control labels and buttons
            $('.map-speed-label').text(this.translate('Speed'));

            // Reset to Default buttons (individual control resets only)
            $('#map-font-size-reset').text(this.translate('Reset to Default'));
            $('#map-line-spacing-reset').text(this.translate('Reset to Default'));
            //reset to default buttons for options letter spacing & text alignement
            $('#map-letter-spacing-reset').text(this.translate('Reset to Default'));
            $('#map-text-alignment-reset').text(this.translate('Reset to Default'));

            // Header reset button (only translate the span, keep the icon)
            $('#map-reset-category span').text(this.translate('Reset'));

            // Line spacing labels
            $('.map-spacing-label-min').text(this.translate('Tight'));
            $('.map-spacing-label-center').text(this.translate('Normal'));
            $('.map-spacing-label-max').text(this.translate('Wide'));

            // Default status values
            $('#map-font-size-value').text(this.translate('Default'));
            $('#map-line-spacing-value').text(this.translate('Default'));
            $('#map-theme-name').text(this.translate('Default'));

            // Update theme names in the themes array and UI
            this.updateThemeTranslations();
        };

        /**
         * Update theme translations
         */
        window.MapAccessibility.prototype.updateThemeTranslations = function() {
            // Update theme names in the themes array
            if (this.themes) {
                this.themes.forEach(theme => {
                    switch(theme.id) {
                        case 'normal':
                            theme.name = this.translate('Default');
                            theme.description = this.translate('Standard website appearance');
                            break;
                        case 'monochrome':
                            theme.name = this.translate('Monochrome');
                            theme.description = this.translate('Grayscale colors for reduced visual distraction');
                            break;
                        case 'low-saturation':
                            theme.name = this.translate('Low Saturation');
                            theme.description = this.translate('Reduced color intensity for comfortable viewing');
                            break;
                        case 'high-saturation':
                            theme.name = this.translate('High Saturation');
                            theme.description = this.translate('Enhanced color intensity for vibrant viewing');
                            break;
                        case 'dark':
                            theme.name = this.translate('Dark Mode');
                            theme.description = this.translate('Dark background with light text for low-light environments');
                            break;
                        case 'high-contrast':
                            theme.name = this.translate('High Contrast');
                            theme.description = this.translate('Black and white for maximum readability');
                            break;
                        case 'sepia':
                            theme.name = this.translate('Sepia');
                            theme.description = this.translate('Warm, paper-like colors for comfortable reading');
                            break;
                        case 'colorblind':
                            theme.name = this.translate('Color Blind Friendly');
                            theme.description = this.translate('Optimized colors for color vision deficiency');
                            break;
                    }
                });

                // Re-render theme selector if it's visible
                if ($('.map-theme-selector').length > 0) {
                    this.updateThemeSelector();
                }
            }
        };

        // Additional translation methods will be added here
    }

})(jQuery);
