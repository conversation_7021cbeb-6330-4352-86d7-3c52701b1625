<?php
/**
 * CSS Dependency Checker for Modern Accessibility Plugin
 * Verifies all JavaScript-dependent CSS classes are present in split files
 */

class MAP_CSS_Dependency_Checker {
    
    private $js_dependent_classes = [
        // Toggle States
        'active',
        'map-panel-active',
        'map-panel-open',
        'map-fading-out',
        
        // Modal View Classes
        'map-modal-view-active',
        'map-view-entering-forward',
        'map-view-entering-backward',
        'map-view-exiting-forward',
        'map-view-exiting-backward',
        
        // Body Theme Classes
        'map-theme-normal',
        'map-theme-dark',
        'map-theme-high-contrast',
        'map-theme-sepia',
        'map-theme-monochrome',
        'map-theme-low-saturation',
        'map-theme-high-saturation',
        'map-theme-colorblind',
        'map-theme-custom',
        
        // Body Feature Classes
        'dyslexic-font',
        'dyslexic-font-loading',
        'map-adhd-focus-mode',
        'map-big-cursor-mode',
        'map-big-cursor-active',
        'map-text-magnification-mode',
        'map-font-size-active',
        'map-line-spacing-active',
        
        // Widget States
        'map-dark-mode',
        'map-position-top-left',
        'map-position-top-right',
        'map-position-bottom-left',
        'map-position-bottom-right',
        
        // Language & UI States
        'map-language-active',
        'map-theme-active',
        'map-reset-feedback',
        'applying',
        'smooth',
        'hidden',
        
        // Button States
        'map-loading',
        'map-stop-btn',
        'visible',
        
        // Text Highlighting
        'map-highlight-text',
        
        // Reset Confirmation
        'map-reset-confirmation-show'
    ];
    
    private $css_files = [
        'base.css',
        'shared/features.css',
        'shared/toggles.css',
        'shared/modal.css',
        'components/category-list.css',
        'components/text-options.css',
        'components/contrast-colors.css',
        'components/navigation.css',
        'components/preferences.css',
        'themes/visual-themes.css',
        'themes/dark-mode.css',
        'responsive.css'
    ];
    
    public function checkAllDependencies() {
        echo "<h2>🔍 CSS Dependency Check Results</h2>\n";
        
        $missing_classes = [];
        $found_classes = [];
        
        foreach ($this->js_dependent_classes as $class) {
            $found_in_files = $this->findClassInFiles($class);
            
            if (empty($found_in_files)) {
                $missing_classes[] = $class;
                echo "<div style='color: red;'>❌ MISSING: .{$class}</div>\n";
            } else {
                $found_classes[$class] = $found_in_files;
                echo "<div style='color: green;'>✅ FOUND: .{$class} in " . implode(', ', $found_in_files) . "</div>\n";
            }
        }
        
        echo "\n<h3>📊 Summary:</h3>\n";
        echo "<p><strong>Total Classes Checked:</strong> " . count($this->js_dependent_classes) . "</p>\n";
        echo "<p><strong>Found:</strong> " . count($found_classes) . "</p>\n";
        echo "<p><strong>Missing:</strong> " . count($missing_classes) . "</p>\n";
        
        if (!empty($missing_classes)) {
            echo "\n<h3>🚨 CRITICAL: Missing Classes</h3>\n";
            echo "<p>These classes are used by JavaScript but not found in any CSS file:</p>\n";
            foreach ($missing_classes as $class) {
                echo "<li>.{$class}</li>\n";
            }
            echo "\n<p><strong>⚠️ DO NOT DELETE frontend.css until these are resolved!</strong></p>\n";
        } else {
            echo "\n<h3>🎉 SUCCESS: All Dependencies Found</h3>\n";
            echo "<p>All JavaScript-dependent classes are present in the split CSS files.</p>\n";
            echo "<p><strong>✅ Safe to replace frontend.css with modular system!</strong></p>\n";
        }
        
        return empty($missing_classes);
    }
    
    private function findClassInFiles($class) {
        $found_in = [];
        
        foreach ($this->css_files as $file) {
            $file_path = __DIR__ . '/' . $file;
            
            if (file_exists($file_path)) {
                $content = file_get_contents($file_path);
                
                // Search for the class with various patterns
                $patterns = [
                    "/\.{$class}\s*\{/",           // .class {
                    "/\.{$class}\s*,/",           // .class,
                    "/\.{$class}\s*:/",           // .class:hover
                    "/\.{$class}\./",             // .class.other
                    "/\.{$class}\s+/",            // .class descendant
                    "/body\.{$class}/",           // body.class
                    "/html\.{$class}/",           // html.class
                ];
                
                foreach ($patterns as $pattern) {
                    if (preg_match($pattern, $content)) {
                        $found_in[] = $file;
                        break; // Found in this file, move to next file
                    }
                }
            }
        }
        
        return array_unique($found_in);
    }
    
    public function generateMissingClassesCSS($missing_classes) {
        if (empty($missing_classes)) {
            return '';
        }
        
        $css = "/* Missing JavaScript-dependent classes */\n";
        
        foreach ($missing_classes as $class) {
            // Generate basic CSS rules based on class name patterns
            if (strpos($class, 'active') !== false) {
                $css .= ".{$class} {\n    /* Active state styles */\n}\n\n";
            } elseif (strpos($class, 'loading') !== false) {
                $css .= ".{$class} {\n    /* Loading state styles */\n}\n\n";
            } elseif (strpos($class, 'hidden') !== false) {
                $css .= ".{$class} {\n    display: none !important;\n}\n\n";
            } elseif (strpos($class, 'visible') !== false) {
                $css .= ".{$class} {\n    display: block !important;\n}\n\n";
            } else {
                $css .= ".{$class} {\n    /* Add styles as needed */\n}\n\n";
            }
        }
        
        return $css;
    }
    
    public function checkSpecificFeatures() {
        echo "<h3>🎯 Feature-Specific Dependency Check</h3>\n";
        
        $feature_checks = [
            'Modal System' => ['map-modal-view-active', 'map-view-entering-forward', 'map-view-exiting-forward'],
            'Theme System' => ['map-theme-dark', 'map-theme-custom', 'map-theme-active'],
            'Toggle System' => ['active', 'map-panel-active'],
            'Dark Mode' => ['map-dark-mode'],
            'ADHD Focus' => ['map-adhd-focus-mode'],
            'Big Cursor' => ['map-big-cursor-active', 'map-big-cursor-mode'],
            'Text Selection' => ['map-highlight-text'],
            'Loading States' => ['map-loading', 'map-stop-btn']
        ];
        
        foreach ($feature_checks as $feature => $classes) {
            echo "<h4>{$feature}</h4>\n";
            $all_found = true;
            
            foreach ($classes as $class) {
                $found_in = $this->findClassInFiles($class);
                if (empty($found_in)) {
                    echo "<div style='color: red;'>❌ .{$class} - MISSING</div>\n";
                    $all_found = false;
                } else {
                    echo "<div style='color: green;'>✅ .{$class} - Found in " . implode(', ', $found_in) . "</div>\n";
                }
            }
            
            if ($all_found) {
                echo "<p><strong>✅ {$feature} - All dependencies satisfied</strong></p>\n";
            } else {
                echo "<p><strong>⚠️ {$feature} - Missing dependencies!</strong></p>\n";
            }
            echo "<hr>\n";
        }
    }
}

// Usage
if (isset($_GET['check']) || php_sapi_name() === 'cli') {
    $checker = new MAP_CSS_Dependency_Checker();
    
    echo "<html><head><title>CSS Dependency Check</title></head><body>\n";
    echo "<h1>🔍 Modern Accessibility Plugin - CSS Dependency Check</h1>\n";
    
    $all_dependencies_found = $checker->checkAllDependencies();
    
    echo "<hr>\n";
    $checker->checkSpecificFeatures();
    
    if (!$all_dependencies_found) {
        echo "<hr>\n";
        echo "<h3>🛠️ Recommended Action</h3>\n";
        echo "<p>1. Review the missing classes above</p>\n";
        echo "<p>2. Add them to appropriate CSS files</p>\n";
        echo "<p>3. Re-run this check</p>\n";
        echo "<p>4. Only then replace frontend.css</p>\n";
    }
    
    echo "</body></html>\n";
}
?>
