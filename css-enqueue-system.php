<?php
/**
 * Modern Accessibility Plugin - CSS Enqueue System
 * Component-Based + Shared Architecture CSS Loading
 * 
 * Replace the single frontend.css with modular CSS files
 * for better maintainability and performance
 */

class MAP_CSS_Enqueue_System {
    
    private $plugin_url;
    private $version;
    
    public function __construct($plugin_url, $version = '1.0.0') {
        $this->plugin_url = $plugin_url;
        $this->version = $version;
    }
    
    /**
     * Enqueue all CSS files in the correct order
     */
    public function enqueue_all_styles() {
        // Add debug comment to HTML
        add_action('wp_head', function() {
            echo "<!-- MAP: Modular CSS System Active -->\n";
        });

        // 1. Base styles (must load first)
        $this->enqueue_base_styles();

        // 2. Shared components (load before specific components)
        $this->enqueue_shared_styles();

        // 3. UI components (load based on active features)
        $this->enqueue_component_styles();

        // 4. Theme and responsive styles (load last)
        $this->enqueue_theme_styles();
    }
    
    /**
     * Enqueue base foundation styles
     */
    private function enqueue_base_styles() {
        wp_enqueue_style(
            'map-base',
            $this->plugin_url . 'assets/css/base.css',
            array(),
            $this->version,
            'all'
        );
    }
    
    /**
     * Enqueue shared component styles
     */
    private function enqueue_shared_styles() {
        // Features (toggles, icons, content)
        wp_enqueue_style(
            'map-shared-features',
            $this->plugin_url . 'assets/css/shared/features.css',
            array('map-base'),
            $this->version,
            'all'
        );
        
        // Toggle switches
        wp_enqueue_style(
            'map-shared-toggles',
            $this->plugin_url . 'assets/css/shared/toggles.css',
            array('map-base'),
            $this->version,
            'all'
        );
        
        // Modal system
        wp_enqueue_style(
            'map-shared-modal',
            $this->plugin_url . 'assets/css/shared/modal.css',
            array('map-base'),
            $this->version,
            'all'
        );
    }
    
    /**
     * Enqueue UI component styles
     */
    private function enqueue_component_styles() {
        $dependencies = array('map-shared-features', 'map-shared-toggles', 'map-shared-modal');
        
        // Category list (always needed)
        wp_enqueue_style(
            'map-component-category-list',
            $this->plugin_url . 'assets/css/components/category-list.css',
            $dependencies,
            $this->version,
            'all'
        );
        
        // Conditional loading based on active features
        if ($this->is_text_options_active()) {
            wp_enqueue_style(
                'map-component-text-options',
                $this->plugin_url . 'assets/css/components/text-options.css',
                $dependencies,
                $this->version,
                'all'
            );
        }
        
        if ($this->is_contrast_colors_active()) {
            wp_enqueue_style(
                'map-component-contrast-colors',
                $this->plugin_url . 'assets/css/components/contrast-colors.css',
                $dependencies,
                $this->version,
                'all'
            );
        }
        
        if ($this->is_navigation_active()) {
            wp_enqueue_style(
                'map-component-navigation',
                $this->plugin_url . 'assets/css/components/navigation.css',
                $dependencies,
                $this->version,
                'all'
            );
        }
        
        if ($this->is_preferences_active()) {
            wp_enqueue_style(
                'map-component-preferences',
                $this->plugin_url . 'assets/css/components/preferences.css',
                $dependencies,
                $this->version,
                'all'
            );
        }
    }
    
    /**
     * Enqueue theme and responsive styles
     */
    private function enqueue_theme_styles() {
        $dependencies = array('map-base', 'map-shared-features');
        
        // Visual themes (if theme system is active)
        if ($this->is_visual_themes_active()) {
            wp_enqueue_style(
                'map-themes-visual',
                $this->plugin_url . 'assets/css/themes/visual-themes.css',
                $dependencies,
                $this->version,
                'all'
            );
        }
        
        // Dark mode (always load for toggle functionality)
        wp_enqueue_style(
            'map-themes-dark-mode',
            $this->plugin_url . 'assets/css/themes/dark-mode.css',
            $dependencies,
            $this->version,
            'all'
        );
        
        // Responsive styles (always load last)
        wp_enqueue_style(
            'map-responsive',
            $this->plugin_url . 'assets/css/responsive.css',
            array('map-base'),
            $this->version,
            'all'
        );
    }
    
    /**
     * Check if text options UI is active
     */
    private function is_text_options_active() {
        // Add your logic here to determine if text options should be loaded
        // For example, check user settings, admin options, etc.
        return true; // Load by default for now
    }
    
    /**
     * Check if contrast & colors UI is active
     */
    private function is_contrast_colors_active() {
        // Add your logic here
        return true; // Load by default for now
    }
    
    /**
     * Check if navigation UI is active
     */
    private function is_navigation_active() {
        // Add your logic here
        return true; // Load by default for now
    }
    
    /**
     * Check if preferences UI is active
     */
    private function is_preferences_active() {
        // Add your logic here
        return true; // Load by default for now
    }
    
    /**
     * Check if visual themes are active
     */
    private function is_visual_themes_active() {
        // Add your logic here
        return true; // Load by default for now
    }
    
    /**
     * Alternative: Load only essential styles for performance
     */
    public function enqueue_essential_styles_only() {
        // Base + shared components only
        $this->enqueue_base_styles();
        $this->enqueue_shared_styles();
        
        // Category list (always needed)
        wp_enqueue_style(
            'map-component-category-list',
            $this->plugin_url . 'assets/css/components/category-list.css',
            array('map-shared-features'),
            $this->version,
            'all'
        );
        
        // Dark mode toggle
        wp_enqueue_style(
            'map-themes-dark-mode',
            $this->plugin_url . 'assets/css/themes/dark-mode.css',
            array('map-base'),
            $this->version,
            'all'
        );
        
        // Responsive
        wp_enqueue_style(
            'map-responsive',
            $this->plugin_url . 'assets/css/responsive.css',
            array('map-base'),
            $this->version,
            'all'
        );
    }
    
    /**
     * Load specific component on demand (AJAX)
     */
    public function enqueue_component_on_demand($component_name) {
        $dependencies = array('map-shared-features', 'map-shared-toggles', 'map-shared-modal');
        
        switch ($component_name) {
            case 'text-options':
                wp_enqueue_style(
                    'map-component-text-options',
                    $this->plugin_url . 'assets/css/components/text-options.css',
                    $dependencies,
                    $this->version,
                    'all'
                );
                break;
                
            case 'contrast-colors':
                wp_enqueue_style(
                    'map-component-contrast-colors',
                    $this->plugin_url . 'assets/css/components/contrast-colors.css',
                    $dependencies,
                    $this->version,
                    'all'
                );
                break;
                
            case 'navigation':
                wp_enqueue_style(
                    'map-component-navigation',
                    $this->plugin_url . 'assets/css/components/navigation.css',
                    $dependencies,
                    $this->version,
                    'all'
                );
                break;
                
            case 'preferences':
                wp_enqueue_style(
                    'map-component-preferences',
                    $this->plugin_url . 'assets/css/components/preferences.css',
                    $dependencies,
                    $this->version,
                    'all'
                );
                break;
        }
    }
}

// Usage Example:
/*
// In your main plugin file, replace the old frontend.css enqueue with:

$css_enqueue = new MAP_CSS_Enqueue_System(plugin_dir_url(__FILE__), '1.0.0');

// Option 1: Load all styles
$css_enqueue->enqueue_all_styles();

// Option 2: Load only essential styles for better performance
// $css_enqueue->enqueue_essential_styles_only();

// Option 3: Load specific components on demand
// $css_enqueue->enqueue_component_on_demand('text-options');
*/
