<?php
/**
 * Performance optimization class
 *
 * @package MyAccessibilityPlugin
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Performance class for My Accessibility Plugin
 *
 * @since 1.0.0
 */
class MAP_Performance {

    /**
     * Initialize performance optimizations
     *
     * @since 1.0.0
     */
    public static function init() {
        // Optimize asset loading
        add_action('wp_enqueue_scripts', array(__CLASS__, 'optimize_asset_loading'), 5);
        
        // Add resource hints
        add_action('wp_head', array(__CLASS__, 'add_resource_hints'), 1);
        
        // Optimize database queries
        add_action('init', array(__CLASS__, 'optimize_database'));
        
        // Cache management
        add_action('wp_footer', array(__CLASS__, 'cache_management'));
        
        // Lazy load non-critical assets
        add_action('wp_footer', array(__CLASS__, 'lazy_load_assets'), 999);
        
        // Cleanup and maintenance
        add_action('wp_scheduled_delete', array(__CLASS__, 'cleanup_expired_data'));
    }

    /**
     * Optimize asset loading
     *
     * @since 1.0.0
     */
    public static function optimize_asset_loading() {
        // Only load assets when needed
        $settings = MAP_Core::get_instance()->get_settings();
        
        if (!$settings['text_to_speech_enabled']) {
            return;
        }
        
        // Defer non-critical JavaScript
        add_filter('script_loader_tag', array(__CLASS__, 'defer_scripts'), 10, 2);
        
        // Preload critical assets
        add_action('wp_head', array(__CLASS__, 'preload_critical_assets'));
        
        // Minify inline styles if not in debug mode
        if (!defined('WP_DEBUG') || !WP_DEBUG) {
            add_filter('wp_get_custom_css', array(__CLASS__, 'minify_css'));
        }
    }

    /**
     * Add resource hints for better performance
     *
     * @since 1.0.0
     */
    public static function add_resource_hints() {
        $settings = MAP_Core::get_instance()->get_settings();
        
        if (!$settings['text_to_speech_enabled']) {
            return;
        }
        
        // DNS prefetch for external resources (if any)
        echo '<link rel="dns-prefetch" href="//fonts.googleapis.com">' . "\n";
        
        // Preconnect to important origins
        echo '<link rel="preconnect" href="' . esc_url(MAP_PLUGIN_URL) . '">' . "\n";
        
        // Prefetch next likely resources
        if (is_singular()) {
            echo '<link rel="prefetch" href="' . esc_url(MAP_PLUGIN_URL . 'assets/js/frontend.js') . '">' . "\n";
        }
    }

    /**
     * Preload critical assets
     *
     * @since 1.0.0
     */
    public static function preload_critical_assets() {
        // Preload critical CSS - Modular System
        echo '<link rel="preload" href="' . esc_url(MAP_PLUGIN_URL . 'assets/css/base.css') . '" as="style" onload="this.onload=null;this.rel=\'stylesheet\'">' . "\n";
        echo '<link rel="preload" href="' . esc_url(MAP_PLUGIN_URL . 'assets/css/shared/features.css') . '" as="style" onload="this.onload=null;this.rel=\'stylesheet\'">' . "\n";
        
        // Preload critical JavaScript
        echo '<link rel="preload" href="' . esc_url(MAP_PLUGIN_URL . 'assets/js/frontend.js') . '" as="script">' . "\n";
    }

    /**
     * Defer non-critical scripts
     *
     * @param string $tag
     * @param string $handle
     * @return string
     * @since 1.0.0
     */
    public static function defer_scripts($tag, $handle) {
        // Scripts to defer - remove map-frontend to fix modal functionality
        $defer_scripts = array('map-admin');

        if (in_array($handle, $defer_scripts)) {
            return str_replace(' src', ' defer src', $tag);
        }

        return $tag;
    }

    /**
     * Optimize database operations
     *
     * @since 1.0.0
     */
    public static function optimize_database() {
        // Use object caching for settings
        add_filter('pre_option_map_settings', array(__CLASS__, 'get_cached_settings'));
        add_action('update_option_map_settings', array(__CLASS__, 'clear_settings_cache'));
        
        // Optimize queries
        add_filter('posts_clauses', array(__CLASS__, 'optimize_content_queries'), 10, 2);
    }

    /**
     * Get cached settings
     *
     * @param mixed $pre_option
     * @return mixed
     * @since 1.0.0
     */
    public static function get_cached_settings($pre_option) {
        $cache_key = 'map_settings_cache';
        $cached = wp_cache_get($cache_key, 'map_plugin');
        
        if ($cached !== false) {
            return $cached;
        }
        
        return $pre_option;
    }

    /**
     * Clear settings cache
     *
     * @param mixed $old_value
     * @param mixed $value
     * @since 1.0.0
     */
    public static function clear_settings_cache($old_value, $value) {
        wp_cache_delete('map_settings_cache', 'map_plugin');
        wp_cache_set('map_settings_cache', $value, 'map_plugin', HOUR_IN_SECONDS);
    }

    /**
     * Optimize content queries
     *
     * @param array $clauses
     * @param WP_Query $query
     * @return array
     * @since 1.0.0
     */
    public static function optimize_content_queries($clauses, $query) {
        // Only optimize our plugin queries
        if (!$query->get('map_query')) {
            return $clauses;
        }
        
        // Add indexes hint for better performance
        global $wpdb;
        $clauses['join'] .= " USE INDEX (PRIMARY)";
        
        return $clauses;
    }

    /**
     * Cache management
     *
     * @since 1.0.0
     */
    public static function cache_management() {
        // Cache processed text content
        if (is_singular()) {
            global $post;
            $cache_key = 'map_processed_content_' . $post->ID;

            if (false === get_transient($cache_key)) {
                $content = apply_filters('the_content', $post->post_content);

                // Get MAP_Core instance and use the text_to_speech instance method
                try {
                    if (class_exists('MAP_Core')) {
                        $core = MAP_Core::get_instance();
                        if ($core && $core->text_to_speech) {
                            $processed = $core->text_to_speech->process_text($content);
                        } else {
                            // Fallback processing if instance not available
                            $processed = wp_strip_all_tags($content);
                            $processed = preg_replace('/\s+/', ' ', $processed);
                            $processed = trim($processed);
                        }
                    } else {
                        // Fallback processing if class not available
                        $processed = wp_strip_all_tags($content);
                        $processed = preg_replace('/\s+/', ' ', $processed);
                        $processed = trim($processed);
                    }
                } catch (Exception $e) {
                    // Fallback processing on error
                    $processed = wp_strip_all_tags($content);
                    $processed = preg_replace('/\s+/', ' ', $processed);
                    $processed = trim($processed);
                }

                set_transient($cache_key, $processed, HOUR_IN_SECONDS);
            }
        }
    }

    /**
     * Lazy load non-critical assets
     *
     * @since 1.0.0
     */
    public static function lazy_load_assets() {
        $settings = MAP_Core::get_instance()->get_settings();
        
        if (!$settings['text_to_speech_enabled']) {
            return;
        }
        
        ?>
        <script>
        // Lazy load non-critical features
        document.addEventListener('DOMContentLoaded', function() {
            // Load additional features only when needed
            var observer = new IntersectionObserver(function(entries) {
                entries.forEach(function(entry) {
                    if (entry.isIntersecting) {
                        // Load additional functionality
                        observer.unobserve(entry.target);
                    }
                });
            });
            
            var widget = document.getElementById('map-accessibility-widget');
            if (widget) {
                observer.observe(widget);
            }
        });
        </script>
        <?php
    }

    /**
     * Minify CSS
     *
     * @param string $css
     * @return string
     * @since 1.0.0
     */
    public static function minify_css($css) {
        // Remove comments
        $css = preg_replace('!/\*[^*]*\*+([^/][^*]*\*+)*/!', '', $css);
        
        // Remove unnecessary whitespace
        $css = str_replace(array("\r\n", "\r", "\n", "\t", '  ', '    ', '    '), '', $css);
        
        // Remove trailing semicolon before closing brace
        $css = str_replace(';}', '}', $css);
        
        return $css;
    }

    /**
     * Cleanup expired data
     *
     * @since 1.0.0
     */
    public static function cleanup_expired_data() {
        global $wpdb;
        
        // Clean up expired transients
        $wpdb->query("DELETE FROM {$wpdb->options} WHERE option_name LIKE '_transient_timeout_map_%' AND option_value < UNIX_TIMESTAMP()");
        $wpdb->query("DELETE FROM {$wpdb->options} WHERE option_name LIKE '_transient_map_%' AND option_name NOT IN (SELECT REPLACE(option_name, '_timeout', '') FROM {$wpdb->options} WHERE option_name LIKE '_transient_timeout_map_%')");
        
        // Clean up old cache entries
        wp_cache_flush_group('map_plugin');
    }

    /**
     * Get performance metrics
     *
     * @return array
     * @since 1.0.0
     */
    public static function get_performance_metrics() {
        $metrics = array();
        
        // Memory usage
        $metrics['memory_usage'] = memory_get_usage(true);
        $metrics['memory_peak'] = memory_get_peak_usage(true);
        
        // Database queries
        $metrics['db_queries'] = get_num_queries();
        
        // Cache hit ratio
        if (function_exists('wp_cache_get_stats')) {
            $cache_stats = wp_cache_get_stats();
            $metrics['cache_hits'] = $cache_stats['hits'] ?? 0;
            $metrics['cache_misses'] = $cache_stats['misses'] ?? 0;
        }
        
        // Plugin-specific metrics
        $metrics['settings_cached'] = wp_cache_get('map_settings_cache', 'map_plugin') !== false;
        
        return $metrics;
    }

    /**
     * Optimize images (if any are added in future)
     *
     * @param string $image_path
     * @return string
     * @since 1.0.0
     */
    public static function optimize_image($image_path) {
        // WebP support check
        if (function_exists('imagewebp') && strpos($_SERVER['HTTP_ACCEPT'], 'image/webp') !== false) {
            $webp_path = str_replace(array('.jpg', '.jpeg', '.png'), '.webp', $image_path);
            if (file_exists($webp_path)) {
                return $webp_path;
            }
        }
        
        return $image_path;
    }

    /**
     * Critical CSS inlining
     *
     * @since 1.0.0
     */
    public static function inline_critical_css() {
        $critical_css = '
        .map-accessibility-widget{position:fixed;z-index:999999;font-family:-apple-system,BlinkMacSystemFont,"Segoe UI",Roboto,sans-serif}
        .map-main-toggle{display:flex;align-items:center;gap:8px;background:var(--map-primary-color);color:#fff;border:none;border-radius:8px;padding:12px 16px;cursor:pointer;font-size:14px;font-weight:600;box-shadow:0 2px 10px rgba(0,0,0,0.1);transition:all 0.3s ease}
        ';
        
        echo '<style id="map-critical-css">' . $critical_css . '</style>';
    }

    /**
     * Service Worker registration (for future PWA features)
     *
     * @since 1.0.0
     */
    public static function register_service_worker() {
        if (!is_admin() && 'https' === parse_url(home_url(), PHP_URL_SCHEME)) {
            ?>
            <script>
            if ('serviceWorker' in navigator) {
                navigator.serviceWorker.register('<?php echo MAP_PLUGIN_URL; ?>sw.js');
            }
            </script>
            <?php
        }
    }

    /**
     * Preload key resources
     *
     * @since 1.0.0
     */
    public static function preload_resources() {
        $resources = array(
            MAP_PLUGIN_URL . 'assets/css/base.css' => 'style',
            MAP_PLUGIN_URL . 'assets/css/shared/features.css' => 'style',
            MAP_PLUGIN_URL . 'assets/js/modules/core.js' => 'script',
            MAP_PLUGIN_URL . 'assets/js/modules/init.js' => 'script'
        );
        
        foreach ($resources as $url => $type) {
            echo '<link rel="preload" href="' . esc_url($url) . '" as="' . esc_attr($type) . '">' . "\n";
        }
    }

    /**
     * Compress output
     *
     * @param string $buffer
     * @return string
     * @since 1.0.0
     */
    public static function compress_output($buffer) {
        if (!defined('WP_DEBUG') || !WP_DEBUG) {
            // Remove unnecessary whitespace
            $buffer = preg_replace('/\s+/', ' ', $buffer);
            $buffer = preg_replace('/>\s+</', '><', $buffer);
        }
        
        return $buffer;
    }
}
