<?php
/**
 * Text to Speech functionality class
 *
 * @package MyAccessibilityPlugin
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Text to Speech class for My Accessibility Plugin
 *
 * @since 1.0.0
 */
class MAP_Text_To_Speech {

    /**
     * Constructor
     *
     * @since 1.0.0
     */
    public function __construct() {
        $this->init();
    }

    /**
     * Initialize text to speech functionality
     *
     * @since 1.0.0
     */
    private function init() {
        // Add hooks
        // Removed add_speech_button from wp_footer since we now use the main accessibility widget
        add_filter('the_content', array($this, 'add_content_markers'));
    }

    /**
     * Add speech button to footer
     *
     * @since 1.0.0
     */
    public function add_speech_button() {
        $settings = MAP_Core::get_instance()->get_settings();
        
        if (!$settings['text_to_speech_enabled']) {
            return;
        }
        
        $position_class = 'map-position-' . str_replace('_', '-', $settings['widget_position']);
        $style_class = 'map-style-' . $settings['widget_style'];
        $size_class = 'map-size-' . $settings['button_size'];
        
        ?>
        <div id="map-speech-widget" class="map-speech-widget <?php echo esc_attr($position_class . ' ' . $style_class . ' ' . $size_class); ?>">
            <button id="map-speech-button" class="map-speech-button" type="button" aria-label="<?php esc_attr_e('Listen to page content', MAP_TEXT_DOMAIN); ?>">
                <span class="map-button-icon">
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M3 9v6h4l5 5V4L7 9H3zm13.5 3c0-1.77-1.02-3.29-2.5-4.03v8.05c1.48-.73 2.5-2.25 2.5-4.02zM14 3.23v2.06c2.89.86 5 3.54 5 6.71s-2.11 5.85-5 6.71v2.06c4.01-.91 7-4.49 7-8.77s-2.99-7.86-7-8.77z"/>
                    </svg>
                </span>
                <span class="map-button-text"><?php echo esc_html($settings['button_text']); ?></span>
            </button>
            
            <div id="map-speech-controls" class="map-speech-controls" style="display: none;">
                <button id="map-play-pause" class="map-control-button" type="button" aria-label="<?php esc_attr_e('Play/Pause', MAP_TEXT_DOMAIN); ?>">
                    <span class="map-play-icon">
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M8 5v14l11-7z"/>
                        </svg>
                    </span>
                    <span class="map-pause-icon" style="display: none;">
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M6 19h4V5H6v14zm8-14v14h4V5h-4z"/>
                        </svg>
                    </span>
                </button>
                
                <button id="map-stop" class="map-control-button" type="button" aria-label="<?php esc_attr_e('Stop', MAP_TEXT_DOMAIN); ?>">
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M6 6h12v12H6z"/>
                    </svg>
                </button>
                
                <div class="map-progress-container">
                    <div id="map-progress-bar" class="map-progress-bar">
                        <div id="map-progress-fill" class="map-progress-fill"></div>
                    </div>
                </div>
                
                <div class="map-speed-control">
                    <label for="map-speed-slider" class="map-speed-label"><?php esc_html_e('Speed', MAP_TEXT_DOMAIN); ?></label>
                    <input type="range" id="map-speed-slider" class="map-speed-slider" min="0.5" max="2" step="0.1" value="<?php echo esc_attr($settings['speech_rate']); ?>">
                </div>
            </div>
        </div>
        
        <style>
            :root {
                --map-button-color: <?php echo esc_attr($settings['button_color']); ?>;
            }
        </style>
        <?php
    }

    /**
     * Add content markers for better text extraction
     *
     * @param string $content
     * @return string
     * @since 1.0.0
     */
    public function add_content_markers($content) {
        $settings = MAP_Core::get_instance()->get_settings();
        
        if (!$settings['text_to_speech_enabled'] || is_admin()) {
            return $content;
        }
        
        // Add data attributes to help with text extraction
        $content = '<div class="map-content-wrapper" data-map-content="true">' . $content . '</div>';
        
        return $content;
    }

    /**
     * Process text for speech synthesis
     *
     * @param string $text
     * @return string
     * @since 1.0.0
     */
    public function process_text($text) {
        // Remove HTML tags
        $text = wp_strip_all_tags($text);
        
        // Remove extra whitespace
        $text = preg_replace('/\s+/', ' ', $text);
        
        // Remove special characters that might cause issues
        $text = preg_replace('/[^\w\s\.,!?;:\-\(\)]/u', '', $text);
        
        // Trim whitespace
        $text = trim($text);
        
        // Apply filters for extensibility
        $text = apply_filters('map_process_speech_text', $text);
        
        return $text;
    }

    /**
     * Get page content for speech
     *
     * @return array
     * @since 1.0.0
     */
    public function get_page_content() {
        global $post;
        
        $content = array();
        
        if (is_singular()) {
            // Get post title
            $content['title'] = get_the_title($post);
            
            // Get post content
            $post_content = apply_filters('the_content', $post->post_content);
            $content['content'] = $this->process_text($post_content);
            
            // Get excerpt if available
            if (!empty($post->post_excerpt)) {
                $content['excerpt'] = $this->process_text($post->post_excerpt);
            }
        } elseif (is_archive()) {
            // Archive page
            $content['title'] = get_the_archive_title();
            $content['description'] = get_the_archive_description();
        } elseif (is_home()) {
            // Blog home
            $content['title'] = get_bloginfo('name');
            $content['description'] = get_bloginfo('description');
        }
        
        return apply_filters('map_page_content', $content);
    }

    /**
     * Extract text from specific elements
     *
     * @param string $selector
     * @return string
     * @since 1.0.0
     */
    public function extract_element_text($selector) {
        // This would be handled by JavaScript on the frontend
        // This method is for future server-side processing if needed
        return '';
    }

    /**
     * Get supported voices (for future enhancement)
     *
     * @return array
     * @since 1.0.0
     */
    public function get_supported_voices() {
        // This will be handled by JavaScript Web Speech API
        // Placeholder for future server-side voice management
        return array();
    }

    /**
     * Check if text-to-speech is supported
     *
     * @return bool
     * @since 1.0.0
     */
    public function is_supported() {
        // Basic check - actual support detection happens in JavaScript
        return true;
    }

    /**
     * Get speech settings for JavaScript
     *
     * @return array
     * @since 1.0.0
     */
    public function get_speech_settings() {
        $settings = MAP_Core::get_instance()->get_settings();
        
        return array(
            'rate' => $settings['speech_rate'],
            'pitch' => $settings['speech_pitch'],
            'volume' => $settings['speech_volume'],
            'auto_highlight' => $settings['auto_highlight'],
            'keyboard_shortcuts' => $settings['keyboard_shortcuts']
        );
    }

    /**
     * Log speech usage (for analytics)
     *
     * @param array $data
     * @since 1.0.0
     */
    public function log_usage($data) {
        // Usage logging functionality can be implemented here
        // Could store in custom table or send to external service
    }
}
