# Accessibility Plugin Demo

## How to Run the Demo

Due to browser security restrictions (CORS), the demo needs to be served via HTTP rather than opened directly as a file.

### Option 1: Using Node.js (Recommended)
```bash
cd landing-page
npx http-server -p 8000
```
Then open: http://127.0.0.1:8000/landing-page.html

### Option 2: Using Python
```bash
cd landing-page
python -m http.server 8000
```
Then open: http://127.0.0.1:8000/landing-page.html

### Option 3: Using PHP (if you have XAMPP)
```bash
cd landing-page
php -S localhost:8000
```
Then open: http://localhost:8000/landing-page.html

## Features

The demo includes all accessibility features:

### Text Options
- Text-to-Speech
- Dyslexic Font
- Reading Guide
- Font Size Control
- Line Spacing Control

### Colors & Contrast
- Visual Themes (8 different themes)
- Color Studio (custom color picker)

### Navigation
- ADHD Focus Mode
- Big Cursor
- Text Magnification

### Preferences
- Dark Mode
- Menu Position (4 positions)
- Language Selection (9 languages)

## Language Support

The demo supports 9 languages with full translation:
- 🇺🇸 English
- 🇫🇷 French (Français)
- 🇪🇸 Spanish (Español)
- 🇩🇪 German (Deutsch)
- 🇮🇹 Italian (Italiano)
- 🇵🇹 Portuguese (Português)
- 🇸🇦 Arabic (العربية)
- 🇨🇳 Chinese (中文)
- 🇷🇺 Russian (Русский)

## Testing Language Switching

1. Open the demo using one of the HTTP server methods above
2. Click the accessibility button (bottom right)
3. Navigate to **Preferences** → **Language**
4. Click on any language to see the interface translate immediately
5. The checkmark (✓) will move to the selected language
6. All text in the interface will update to the selected language

## Note

The demo is 100% identical to the original WordPress plugin in terms of functionality and appearance.
