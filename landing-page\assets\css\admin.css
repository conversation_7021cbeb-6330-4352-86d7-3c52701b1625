/**
 * Admin styles for My Accessibility Plugin
 *
 * @package MyAccessibilityPlugin
 * @since 1.0.0
 */

/* Admin Container Layout */
.map-admin-container {
    display: flex;
    gap: 20px;
    margin-top: 20px;
}

.map-admin-main {
    flex: 1;
    max-width: 800px;
}

.map-admin-sidebar {
    width: 300px;
    flex-shrink: 0;
}

/* Admin Boxes */
.map-admin-box {
    background: #fff;
    border: 1px solid #c3c4c7;
    border-radius: 4px;
    padding: 20px;
    margin-bottom: 20px;
    box-shadow: 0 1px 1px rgba(0, 0, 0, 0.04);
}

.map-admin-box h3 {
    margin-top: 0;
    margin-bottom: 15px;
    font-size: 16px;
    font-weight: 600;
    color: #1d2327;
}

.map-admin-box p {
    margin-bottom: 15px;
    color: #646970;
    line-height: 1.5;
}

.map-admin-box .button {
    margin-right: 10px;
    margin-bottom: 10px;
}

/* Settings Form Styling */
.form-table th {
    width: 200px;
    padding: 20px 10px 20px 0;
    vertical-align: top;
}

.form-table td {
    padding: 15px 10px 20px 0;
    vertical-align: top;
}

/* Range Slider Styling */
.map-range-slider {
    width: 300px;
    height: 6px;
    border-radius: 3px;
    background: #ddd;
    outline: none;
    -webkit-appearance: none;
    margin-right: 10px;
}

.map-range-slider::-webkit-slider-thumb {
    -webkit-appearance: none;
    appearance: none;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background: #0073aa;
    cursor: pointer;
    border: 2px solid #fff;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.map-range-slider::-moz-range-thumb {
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background: #0073aa;
    cursor: pointer;
    border: 2px solid #fff;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.map-range-value {
    display: inline-block;
    min-width: 40px;
    font-weight: 600;
    color: #0073aa;
    background: #f0f6fc;
    padding: 4px 8px;
    border-radius: 3px;
    font-size: 12px;
}

/* Color Input Styling */
input[type="color"] {
    width: 50px;
    height: 35px;
    border: 1px solid #c3c4c7;
    border-radius: 3px;
    cursor: pointer;
    vertical-align: middle;
}

/* Settings Sections */
.form-table .form-section-title {
    background: #f8f9fa;
    padding: 15px 20px;
    margin: 20px -20px 15px -20px;
    border-left: 4px solid #0073aa;
    font-weight: 600;
    color: #1d2327;
}

/* Description Text */
.description {
    color: #646970;
    font-style: italic;
    margin-top: 5px;
    line-height: 1.4;
}

/* Status Indicators */
.map-status-enabled {
    color: #00a32a;
    font-weight: 600;
}

.map-status-disabled {
    color: #d63638;
    font-weight: 600;
}

/* Dashboard Widget Styles */
.map-dashboard-widget {
    font-size: 13px;
}

.map-status-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 0;
    border-bottom: 1px solid #f0f0f1;
}

.map-status-item:last-child {
    border-bottom: none;
}

.map-status-label {
    font-weight: 600;
    color: #1d2327;
}

.map-status-value {
    font-weight: 500;
}

.map-status-value.enabled {
    color: #00a32a;
}

.map-status-value.disabled {
    color: #d63638;
}

.map-quick-actions {
    margin-top: 15px;
    text-align: center;
}

.map-quick-actions .button {
    margin: 0 5px;
}

/* Admin Bar Styles */
#wpadminbar .map-status-enabled {
    color: #00a32a;
}

#wpadminbar .map-status-disabled {
    color: #d63638;
}

/* Notice Styles */
.notice.map-notice {
    border-left-color: #0073aa;
}

.notice.map-notice p {
    margin: 0.5em 0;
}

/* Import/Export Section */
.map-import-export {
    background: #f8f9fa;
    border: 1px solid #e1e1e1;
    border-radius: 4px;
    padding: 15px;
    margin-top: 15px;
}

.map-import-export h4 {
    margin-top: 0;
    margin-bottom: 10px;
    color: #1d2327;
}

.map-import-export input[type="file"] {
    margin-bottom: 10px;
    width: 100%;
}

/* Responsive Admin Layout */
@media screen and (max-width: 1200px) {
    .map-admin-container {
        flex-direction: column;
    }
    
    .map-admin-sidebar {
        width: 100%;
    }
}

@media screen and (max-width: 782px) {
    .map-admin-container {
        margin-top: 10px;
        gap: 15px;
    }
    
    .map-admin-box {
        padding: 15px;
    }
    
    .form-table th,
    .form-table td {
        display: block;
        width: 100%;
        padding: 10px 0;
    }
    
    .form-table th {
        border-bottom: none;
        padding-bottom: 5px;
    }
    
    .map-range-slider {
        width: 100%;
        max-width: 300px;
    }
}

/* Loading States */
.map-loading {
    opacity: 0.6;
    pointer-events: none;
}

.map-loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid #0073aa;
    border-top-color: transparent;
    border-radius: 50%;
    animation: map-spin 1s linear infinite;
}

@keyframes map-spin {
    to {
        transform: rotate(360deg);
    }
}

/* Success/Error Messages */
.map-message {
    padding: 10px 15px;
    border-radius: 4px;
    margin: 15px 0;
    font-weight: 500;
}

.map-message.success {
    background: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.map-message.error {
    background: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

/* Accessibility Enhancements */
.map-admin-container *:focus {
    outline: 2px solid #0073aa;
    outline-offset: 2px;
}

/* High Contrast Support */
@media (prefers-contrast: high) {
    .map-admin-box {
        border-width: 2px;
        border-color: #000;
    }
    
    .map-range-slider::-webkit-slider-thumb {
        border-color: #000;
    }
    
    .map-range-slider::-moz-range-thumb {
        border-color: #000;
    }
}

/* Print Styles */
@media print {
    .map-admin-sidebar {
        display: none;
    }
    
    .map-admin-container {
        flex-direction: column;
    }
    
    .map-admin-box {
        box-shadow: none;
        border: 1px solid #000;
    }
}

/* Plugin Specific Styles */
.map-feature-preview {
    background: #f8f9fa;
    border: 1px solid #e1e1e1;
    border-radius: 4px;
    padding: 15px;
    margin-top: 10px;
}

.map-feature-preview h4 {
    margin-top: 0;
    color: #1d2327;
}

.map-preview-button {
    background: #0073aa;
    color: #fff;
    border: none;
    border-radius: 4px;
    padding: 8px 16px;
    cursor: pointer;
    font-size: 13px;
    margin-top: 10px;
}

.map-preview-button:hover {
    background: #005a87;
}

/* Settings Validation */
.map-field-error {
    border-color: #d63638 !important;
    box-shadow: 0 0 0 1px #d63638;
}

.map-error-message {
    color: #d63638;
    font-size: 12px;
    margin-top: 5px;
    font-style: italic;
}
