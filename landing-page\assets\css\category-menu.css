/* Premium Dark Mode Category Buttons - Enhanced Design */
.map-accessibility-widget.map-dark-mode .map-category-button {
    background: linear-gradient(135deg, var(--map-surface-elevated) 0%, var(--map-gray-200) 100%);
    border: 1px solid var(--map-border-light);
    box-shadow: var(--map-shadow-sm),
                inset 0 1px 0 rgba(255, 255, 255, 0.03);
    transition: all var(--map-transition-base);
}

.map-accessibility-widget.map-dark-mode .map-category-button:hover {
    background: linear-gradient(135deg, var(--map-surface-hover) 0%, var(--map-gray-300) 100%);
    border-color: var(--map-primary);
    box-shadow: var(--map-shadow-md),
                var(--map-glow-primary),
                inset 0 1px 0 rgba(255, 255, 255, 0.05);
    transform: translateY(-2px);
}

.map-accessibility-widget.map-dark-mode .map-category-button:focus-visible {
    outline: none;
    border-color: var(--map-primary);
    box-shadow: 0 0 0 3px rgba(124, 58, 237, 0.3),
                var(--map-shadow-lg);
}

/* Premium Dark Mode Category Icons - Unified with Feature Icons */
.map-accessibility-widget.map-dark-mode .map-category-icon-text {
    background: linear-gradient(135deg, var(--map-primary) 0%, var(--map-primary-light) 100%);
    /* Remove color override to preserve internal icon design */
    box-shadow: 0 4px 12px rgba(99, 102, 241, 0.4);
}

.map-accessibility-widget.map-dark-mode .map-category-icon-colors {
    background: linear-gradient(135deg, var(--map-primary) 0%, var(--map-primary-light) 100%);
    /* Remove color override to preserve internal icon design */
    box-shadow: 0 4px 12px rgba(99, 102, 241, 0.4);
}

.map-accessibility-widget.map-dark-mode .map-category-icon-navigation {
    background: linear-gradient(135deg, var(--map-primary) 0%, var(--map-primary-light) 100%);
    /* Remove color override to preserve internal icon design */
    box-shadow: 0 4px 12px rgba(99, 102, 241, 0.4);
}

.map-accessibility-widget.map-dark-mode .map-category-icon-preferences {
    background: linear-gradient(135deg, var(--map-primary) 0%, var(--map-primary-light) 100%);
    /* Remove color override to preserve internal icon design */
    box-shadow: 0 4px 12px rgba(99, 102, 241, 0.4);
}

/* Dark Mode Icons - Perfect Premium White Internal Details */
/* Set the base color to white so currentColor inherits white */
.map-accessibility-widget.map-dark-mode .map-category-icon svg,
.map-accessibility-widget.map-dark-mode .map-feature-icon svg {
    color: #ffffff !important;
}

/* ===== TEXT CATEGORY ICON FIXES - REVERT TO PROPER STROKE APPEARANCE ===== */

/* Text category icon should remain stroke-based, not filled */
.map-accessibility-widget.map-dark-mode .map-category-icon-text svg {
    color: #ffffff !important;
    fill: none !important;
    stroke: #ffffff !important;
}

/* Ensure all Text category icon elements use stroke, not fill */
.map-accessibility-widget.map-dark-mode .map-category-icon-text svg path,
.map-accessibility-widget.map-dark-mode .map-category-icon-text svg polyline,
.map-accessibility-widget.map-dark-mode .map-category-icon-text svg line {
    fill: none !important;
    stroke: #ffffff !important;
    stroke-width: 12 !important; /* Maintain original stroke-width of 12 */
}

/* Text category icon hover state - maintain white stroke */
.map-accessibility-widget.map-dark-mode .map-category-button:hover .map-category-icon-text svg,
.map-accessibility-widget.map-dark-mode .map-category-button:hover .map-category-icon-text svg path,
.map-accessibility-widget.map-dark-mode .map-category-button:hover .map-category-icon-text svg polyline,
.map-accessibility-widget.map-dark-mode .map-category-button:hover .map-category-icon-text svg line {
    fill: none !important;
    stroke: #ffffff !important;
    color: #ffffff !important;
}

/* ===== GENERAL FEATURE ICON FIXES FOR DARK MODE ===== */

/* Ensure all feature icons with fill="currentColor" are fully white in dark mode */
.map-accessibility-widget.map-dark-mode .map-feature-icon svg[fill="currentColor"],
.map-accessibility-widget.map-dark-mode .map-feature-icon svg path[fill="currentColor"] {
    fill: #ffffff !important;
    color: #ffffff !important;
}

/* Specific fix for any remaining text/letter icons that should be filled */
.map-accessibility-widget.map-dark-mode .map-feature-icon svg path[d*="M8 2h14v4h-5v16h-4V6H8V2z"],
.map-accessibility-widget.map-dark-mode .map-feature-icon svg path[d*="M2 12h8v2.5H7v7.5H5v-7.5H2V12z"] {
    fill: #ffffff !important;
    stroke: none !important;
}

/* Ensure stroke-only elements are white (for outline icons) */
.map-accessibility-widget.map-dark-mode .map-category-icon svg[fill="none"] path,
.map-accessibility-widget.map-dark-mode .map-feature-icon svg[fill="none"] path,
.map-accessibility-widget.map-dark-mode .map-category-icon svg[fill="none"] circle,
.map-accessibility-widget.map-dark-mode .map-feature-icon svg[fill="none"] circle,
.map-accessibility-widget.map-dark-mode .map-category-icon svg[fill="none"] rect,
.map-accessibility-widget.map-dark-mode .map-feature-icon svg[fill="none"] rect,
.map-accessibility-widget.map-dark-mode .map-category-icon svg[fill="none"] text:not([fill]),
.map-accessibility-widget.map-dark-mode .map-feature-icon svg[fill="none"] text:not([fill]) {
    stroke: #ffffff !important;
    fill: none !important;
}

/* Ensure filled elements are white (for solid icons) */
.map-accessibility-widget.map-dark-mode .map-category-icon svg[fill="currentColor"] path:not([fill="none"]),
.map-accessibility-widget.map-dark-mode .map-feature-icon svg[fill="currentColor"] path:not([fill="none"]),
.map-accessibility-widget.map-dark-mode .map-category-icon svg[fill="currentColor"] circle[fill="currentColor"],
.map-accessibility-widget.map-dark-mode .map-feature-icon svg[fill="currentColor"] circle[fill="currentColor"],
.map-accessibility-widget.map-dark-mode .map-category-icon svg[fill="currentColor"] text[fill="currentColor"],
.map-accessibility-widget.map-dark-mode .map-feature-icon svg[fill="currentColor"] text[fill="currentColor"] {
    fill: #ffffff !important;
    stroke: none !important;
}

/* Ensure stroke-only paths within filled SVGs are white */
.map-accessibility-widget.map-dark-mode .map-category-icon svg[fill="currentColor"] path[fill="none"],
.map-accessibility-widget.map-dark-mode .map-feature-icon svg[fill="currentColor"] path[fill="none"] {
    stroke: #ffffff !important;
    fill: none !important;
}

/* Ensure stroke-only icons remain as strokes in light mode */
.map-accessibility-widget .map-feature-icon svg[fill="none"] path,
.map-accessibility-widget .map-feature-icon svg[fill="none"] circle {
    fill: none !important;
    stroke: currentColor !important;
}

/* Big Cursor Icon - Make white in both light and dark modes */
.map-accessibility-widget .map-feature-icon svg path[fill="#ddd"] {
    fill: #ffffff !important;
}

/* Dark Mode Category Icon Hover Effects - Enhanced Glow */
.map-accessibility-widget.map-dark-mode .map-category-button:hover .map-category-icon-text {
    background: linear-gradient(135deg, var(--map-primary-dark) 0%, var(--map-primary) 100%);
    box-shadow: 0 6px 20px rgba(99, 102, 241, 0.5);
    transform: scale(1.05);
}

.map-accessibility-widget.map-dark-mode .map-category-button:hover .map-category-icon-colors {
    background: linear-gradient(135deg, var(--map-primary-dark) 0%, var(--map-primary) 100%);
    box-shadow: 0 6px 20px rgba(99, 102, 241, 0.5);
    transform: scale(1.05);
}

.map-accessibility-widget.map-dark-mode .map-category-button:hover .map-category-icon-navigation {
    background: linear-gradient(135deg, var(--map-primary-dark) 0%, var(--map-primary) 100%);
    box-shadow: 0 6px 20px rgba(99, 102, 241, 0.5);
    transform: scale(1.05);
}

.map-accessibility-widget.map-dark-mode .map-category-button:hover .map-category-icon-preferences {
    background: linear-gradient(135deg, var(--map-primary-dark) 0%, var(--map-primary) 100%);
    box-shadow: 0 6px 20px rgba(99, 102, 241, 0.5);
    transform: scale(1.05);
}

/* Category Buttons */
.map-category-button {
    display: flex;
    align-items: center;
    width: 100%;
    background: var(--map-white);
    border: 1px solid var(--map-gray-200);
    border-radius: var(--map-radius-lg);
    padding: var(--map-space-4);
    cursor: pointer;
    transition: all var(--map-transition-base);
    color: var(--map-gray-700);
    text-align: left;
    position: relative;
    overflow: hidden;
    min-height: 72px;
}

.map-category-button::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, var(--map-primary) 0%, var(--map-primary-light) 100%);
    opacity: 0;
    transition: opacity var(--map-transition-base);
}

.map-category-button:hover:not(.map-category-disabled) {
    background: linear-gradient(135deg, var(--map-gray-50) 0%, var(--map-white) 100%);
    border-color: var(--map-primary);
    transform: translateY(-3px);
    box-shadow: 0 10px 25px rgba(99, 102, 241, 0.15), 0 4px 6px rgba(0, 0, 0, 0.05);
}

.map-category-button:hover:not(.map-category-disabled)::before {
    opacity: 0.03;
}

.map-category-button:active:not(.map-category-disabled) {
    transform: translateY(-1px);
    box-shadow: var(--map-shadow-sm);
}

/* No default focus outline for category buttons */
.map-category-button:focus {
    outline: none;
}

/* Premium Category Icons with Blue Gradient Scheme */
.map-category-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 48px;
    height: 48px;
    border-radius: var(--map-radius-lg);
    margin-right: var(--map-space-4);
    flex-shrink: 0;
    transition: all var(--map-transition-base);
    position: relative;
    z-index: 1;
    box-shadow: 0 2px 8px rgba(99, 102, 241, 0.15);
}

/* Individual Category Icon Colors - Premium Blue Variations */
.map-category-icon-text,
.map-category-icon-colors,
.map-category-icon-navigation,
.map-category-icon-preferences {
    color: var(--map-white);
}

.map-category-icon-text {
    background: linear-gradient(135deg, var(--map-primary) 0%, var(--map-primary-light) 100%);
}

.map-category-icon-colors {
    background: linear-gradient(135deg, var(--map-primary) 0%, var(--map-primary-light) 100%);
}

.map-category-icon-navigation {
    background: linear-gradient(135deg, var(--map-primary) 0%, var(--map-primary-light) 100%);
}

.map-category-icon-preferences {
    background: linear-gradient(135deg, var(--map-primary) 0%, var(--map-primary-light) 100%);
}

/* Premium Hover Effects */
.map-category-button:hover:not(.map-category-disabled) .map-category-icon {
    transform: scale(1.08) translateY(-2px);
    box-shadow: 0 8px 25px rgba(99, 102, 241, 0.3);
}

.map-category-button:hover:not(.map-category-disabled) .map-category-icon-text {
    background: linear-gradient(135deg, var(--map-primary-dark) 0%, var(--map-primary) 100%);
}

.map-category-button:hover:not(.map-category-disabled) .map-category-icon-colors {
    background: linear-gradient(135deg, var(--map-primary-dark) 0%, var(--map-primary) 100%);
}

.map-category-button:hover:not(.map-category-disabled) .map-category-icon-navigation {
    background: linear-gradient(135deg, var(--map-primary-dark) 0%, var(--map-primary) 100%);
}

.map-category-button:hover:not(.map-category-disabled) .map-category-icon-preferences {
    background: linear-gradient(135deg, var(--map-primary-dark) 0%, var(--map-primary) 100%);
}

/* Category Content */
.map-category-content {
    flex: 1;
    position: relative;
    z-index: 1;
}

.map-category-title {
    margin: 0 0 var(--map-space-1) 0;
    font-size: var(--map-font-size-base);
    font-weight: 600;
    color: var(--map-gray-800);
    line-height: 1.4;
    transition: color var(--map-transition-base);
}

.map-category-desc {
    font-size: var(--map-font-size-sm);
    color: var(--map-gray-500);
    margin: 0;
    line-height: 1.5;
    transition: color var(--map-transition-base);
}

/* Premium hover text effects */
.map-category-button:hover:not(.map-category-disabled) .map-category-title {
    color: var(--map-gray-900);
}

.map-category-button:hover:not(.map-category-disabled) .map-category-desc {
    color: var(--map-gray-600);
}

/* Premium Category Arrow - Enhanced Refined Style */
.map-category-arrow {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 32px;
    height: 32px;
    background: rgba(255, 255, 255, 0.8);
    border: 1px solid rgba(99, 102, 241, 0.15);
    border-radius: var(--map-radius-lg);
    color: var(--map-gray-400);
    transition: all var(--map-transition-base), transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    z-index: 1;
    box-shadow: 0 2px 6px rgba(99, 102, 241, 0.08);
    backdrop-filter: blur(4px);
}

.map-category-button:hover:not(.map-category-disabled) .map-category-arrow {
    background: rgba(99, 102, 241, 0.1);
    border-color: var(--map-primary);
    color: var(--map-primary);
    transform: translateX(4px) scale(1.08);
    box-shadow: 0 4px 12px rgba(99, 102, 241, 0.2);
}

/* View Header */
.map-view-header {
    display: flex;
    align-items: center;
    margin-bottom: var(--map-space-6);
    padding-bottom: var(--map-space-4);
    border-bottom: 1px solid var(--map-gray-200);
}

/* Premium Modal Back Button - Blue Gradient Style */
.map-back-button {
    display: flex;
    align-items: center;
    background: linear-gradient(135deg, var(--map-primary) 0%, var(--map-primary-light) 100%);
    border: 1px solid var(--map-primary);
    border-radius: var(--map-radius-lg);
    padding: var(--map-space-3) var(--map-space-4);
    cursor: pointer;
    transition: all var(--map-transition-base);
    color: var(--map-white);
    font-size: var(--map-font-size-sm);
    font-weight: 600;
    margin-right: var(--map-space-4);
    box-shadow: 0 2px 8px rgba(99, 102, 241, 0.15);
    position: relative;
    overflow: hidden;
}

.map-back-button::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(255,255,255,0.2) 0%, rgba(255,255,255,0.05) 100%);
    opacity: 0;
    transition: opacity var(--map-transition-base);
}

.map-back-button:hover {
    background: linear-gradient(135deg, var(--map-primary-dark) 0%, var(--map-primary) 100%);
    color: var(--map-white);
    transform: translateY(-2px) translateX(-2px);
    box-shadow: 0 6px 16px rgba(99, 102, 241, 0.25);
}

.map-back-button:hover::before {
    opacity: 1;
}

.map-back-button:active {
    transform: translateY(-1px) translateX(-1px);
    box-shadow: 0 4px 12px rgba(99, 102, 241, 0.2);
}

/* Default subtle focus for back button */
.map-back-button:focus {
    outline: 2px solid var(--map-gray-400);
    outline-offset: 1px;
}
.map-back-button svg {
    margin-right: var(--map-space-2);
    width: 16px;
    height: 16px;
}

.map-view-title {
    margin: 0;
    font-size: var(--map-font-size-lg);
    font-weight: 600;
    color: var(--map-gray-800);
    flex: 1;
}

/* View Content - Enhanced for Proper Scrolling with Maximum Bottom Space */
.map-view-content {
    padding: var(--map-space-6);
    padding-bottom: var(--map-space-12); /* Maximum bottom padding for generous space */
    min-height: 100%;
    box-sizing: border-box;
}
/* Placeholder Sections */
.map-placeholder-section {
    display: flex;
    align-items: flex-start;
    padding: var(--map-space-6);
    background: var(--map-gray-50);
    border: 1px solid var(--map-gray-200);
    border-radius: var(--map-radius-lg);
    margin-bottom: var(--map-space-4);
}

.map-placeholder-icon {
    font-size: 32px;
    margin-right: var(--map-space-4);
    flex-shrink: 0;
    line-height: 1;
}

.map-placeholder-text {
    flex: 1;
}

.map-placeholder-text p {
    margin: 0 0 var(--map-space-3) 0;
    color: var(--map-gray-600);
    font-size: var(--map-font-size-sm);
    line-height: 1.6;
}

.map-placeholder-text ul {
    margin: 0;
    padding-left: var(--map-space-4);
    color: var(--map-gray-500);
    font-size: var(--map-font-size-sm);
}

.map-placeholder-text li {
    margin-bottom: var(--map-space-1);
    line-height: 1.5;
}
