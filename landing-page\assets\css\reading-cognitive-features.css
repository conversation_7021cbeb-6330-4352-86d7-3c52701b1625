/* ===== DYSLEXIC FONT FEATURE ===== */

/**
 * Dyslexic Font Class
 * Applied to body when dyslexic font is enabled
 * Uses OpenDyslexic font with fallbacks
 */
.dyslexic-font {
    font-family: 'OpenDyslexic', 'OpenDyslexic-Regular', Arial, sans-serif !important;
}

.dyslexic-font * {
    font-family: inherit !important;
}

/* Ensure proper font loading and fallback */
.dyslexic-font h1,
.dyslexic-font h2,
.dyslexic-font h3,
.dyslexic-font h4,
.dyslexic-font h5,
.dyslexic-font h6 {
    font-family: 'OpenDyslexic', 'OpenDyslexic-Bold', Arial, sans-serif !important;
    font-weight: bold;
}

/* Preserve accessibility widget styling when dyslexic font is active */
.dyslexic-font .map-accessibility-widget,
.dyslexic-font .map-accessibility-widget * {
    font-family: -apple-system, BlinkMacSystemFont, '<PERSON><PERSON><PERSON>', Robot<PERSON>, Oxygen, Ubuntu, Cantarell, sans-serif !important;
}

/* Loading state for font */
.dyslexic-font-loading {
    font-display: swap;
}

/* Smooth transition when applying/removing dyslexic font */
body {
    transition: font-family 0.3s ease-in-out;
}

/* Ensure readability improvements */
.dyslexic-font {
    line-height: 1.6 !important;
    letter-spacing: 0.05em !important;
    word-spacing: 0.1em !important;
}

/* Preserve form elements styling */
.dyslexic-font input,
.dyslexic-font textarea,
.dyslexic-font select,
.dyslexic-font button {
    font-family: 'OpenDyslexic', Arial, sans-serif !important;
}

/* Status message styling removed for cleaner UX */

/* ===== READING GUIDE FEATURE ===== */

/**
 * Reading Guide Line
 * A horizontal line that follows the mouse cursor to help users focus on text
 */
.map-reading-guide {
    position: fixed;
    left: 0;
    right: 0;
    width: 100%;
    height: 3px;
    background: rgba(99, 102, 241, 0.6);
    box-shadow: 0 0 8px rgba(99, 102, 241, 0.4);
    z-index: var(--map-z-tooltip);
    pointer-events: none;
    transition: opacity 0.2s ease-in-out;
    border-radius: 1px;
}
/* Smooth animation for guide line movement */
.map-reading-guide.smooth {
    transition: top 0.1s ease-out, opacity 0.2s ease-in-out;
}

/* Hide guide line when not active */
.map-reading-guide.hidden {
    opacity: 0;
    visibility: hidden;
}

/* Ensure guide line doesn't interfere with text selection */
.map-reading-guide {
    user-select: none;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
}

/* Responsive adjustments for mobile devices */
@media (max-width: 768px) {
    .map-reading-guide {
        height: 4px; /* Slightly thicker on mobile for better visibility */
    }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    .map-reading-guide {
        background: rgba(0, 0, 0, 0.8);
        box-shadow: 0 0 4px rgba(0, 0, 0, 0.6);
    }
}

/* ===== DARK MODE SUPPORT FOR READING GUIDE ===== */

/* Transform Reading Guide from blue to purple in dark mode */
.map-accessibility-widget.map-dark-mode ~ .map-reading-guide,
body:has(.map-accessibility-widget.map-dark-mode) .map-reading-guide,
body.map-dark-mode .map-reading-guide {
    background: rgba(124, 58, 237, 0.6) !important;
    box-shadow: 0 0 8px rgba(124, 58, 237, 0.4) !important;
}
/* ===== FONT SIZE CONTROL FEATURE ===== */

/**
 * Font Size Control Section
 * Clean, organized layout for font size adjustment controls
 */

/* ===== PREMIUM FONT SIZE CONTROLS REDESIGN ===== */

/* Main container with premium styling */
.map-font-size-controls {
    display: flex;
    flex-direction: column;
    gap: var(--map-space-4);
    padding: var(--map-space-1);
}

/* Premium control group with sophisticated design */
.map-size-control-group {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: var(--map-space-4);
    padding: var(--map-space-5);
    background: linear-gradient(135deg,
        rgba(255, 255, 255, 0.95) 0%,
        rgba(248, 250, 252, 0.9) 50%,
        rgba(241, 245, 249, 0.85) 100%);
    border: 1px solid rgba(var(--map-primary-rgb), 0.08);
    border-radius: 16px;
    box-shadow:
        0 4px 20px rgba(0, 0, 0, 0.04),
        0 1px 3px rgba(0, 0, 0, 0.06),
        inset 0 1px 0 rgba(255, 255, 255, 0.7);
    position: relative;
    overflow: hidden;
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
}

/* Premium accent line with sophisticated gradient */
.map-size-control-group::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: linear-gradient(90deg,
        transparent 0%,
        rgba(var(--map-primary-rgb), 0.1) 20%,
        rgba(var(--map-primary-rgb), 0.3) 50%,
        rgba(var(--map-primary-rgb), 0.1) 80%,
        transparent 100%);
    border-radius: 16px 16px 0 0;
}

/* Subtle inner glow effect */
.map-size-control-group::after {
    content: '';
    position: absolute;
    top: 2px;
    left: 1px;
    right: 1px;
    bottom: 1px;
    background: linear-gradient(135deg,
        rgba(255, 255, 255, 0.4) 0%,
        rgba(255, 255, 255, 0.1) 50%,
        rgba(255, 255, 255, 0.05) 100%);
    border-radius: 15px;
    pointer-events: none;
    opacity: 0.6;
}

/* Enhanced control buttons with premium styling */
.map-size-control-btn {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: 56px;
    height: 56px;
    background: linear-gradient(135deg,
        var(--map-white) 0%,
        var(--map-gray-50) 50%,
        var(--map-gray-100) 100%);
    border: 3px solid var(--map-primary);
    border-radius: 16px;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    color: var(--map-primary);
    font-size: 10px;
    font-weight: 600;
    gap: 2px;
    box-shadow:
        0 4px 12px rgba(var(--map-primary-rgb), 0.2),
        0 2px 4px rgba(0, 0, 0, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.8);
    position: relative;
    z-index: 2;
}

.map-size-control-btn:hover:not(:disabled) {
    background: linear-gradient(135deg,
        var(--map-primary) 0%,
        var(--map-primary-light) 100%);
    border-color: var(--map-primary-dark);
    color: var(--map-white);
    transform: translateY(-2px) scale(1.05);
    box-shadow:
        0 6px 20px rgba(var(--map-primary-rgb), 0.35),
        0 3px 8px rgba(0, 0, 0, 0.15),
        inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

.map-size-control-btn:active {
    transform: translateY(-1px) scale(1.02);
    box-shadow:
        0 4px 12px rgba(var(--map-primary-rgb), 0.3),
        0 2px 4px rgba(0, 0, 0, 0.1);
}

/* Disabled state styling */
.map-size-control-btn:disabled {
    opacity: 0.4;
    cursor: not-allowed;
    transform: none !important;
    box-shadow:
        0 2px 6px rgba(var(--map-primary-rgb), 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.5);
}

.map-size-control-btn svg {
    transition: all var(--map-transition-base);
}

.map-size-label {
    font-size: 11px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* ===== PREMIUM SIZE INDICATOR REDESIGN ===== */

/* Enhanced size indicator with sophisticated styling */
.map-size-indicator {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 6px;
    padding: var(--map-space-4);
    background: linear-gradient(135deg,
        rgba(var(--map-primary-rgb), 0.08) 0%,
        rgba(var(--map-primary-rgb), 0.12) 50%,
        rgba(var(--map-primary-rgb), 0.06) 100%);
    border: 1px solid rgba(var(--map-primary-rgb), 0.2);
    border-radius: 14px;
    min-width: 90px;
    box-shadow:
        0 2px 8px rgba(var(--map-primary-rgb), 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.6);
    position: relative;
    z-index: 2;
}

/* Premium preview text with enhanced styling */
.map-size-preview {
    font-size: 24px;
    font-weight: 800;
    color: var(--map-primary);
    line-height: 1;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    text-shadow: 0 1px 2px rgba(var(--map-primary-rgb), 0.1);
    letter-spacing: -0.5px;
}

/* Premium percentage badge */
.map-size-percentage {
    font-size: 9px;
    font-weight: 700;
    color: var(--map-white);
    background: linear-gradient(135deg,
        var(--map-primary) 0%,
        var(--map-primary-light) 100%);
    padding: 3px 8px;
    border-radius: 8px;
    border: 1px solid rgba(255, 255, 255, 0.1);
    box-shadow:
        0 2px 6px rgba(var(--map-primary-rgb), 0.25),
        inset 0 1px 0 rgba(255, 255, 255, 0.2);
    text-transform: uppercase;
    letter-spacing: 0.5px;
    min-width: 32px;
    text-align: center;
}

.map-font-size-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 4px;
    padding: 8px 12px;
    background: var(--map-surface);
    border: 2px solid var(--map-border);
    border-radius: 6px;
    color: var(--map-text);
    font-size: 13px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    min-width: 50px;
    height: 36px;
    flex: 1;
}

.map-font-size-btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.map-font-size-btn:active {
    transform: translateY(0);
}

.map-font-size-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none;
}

.map-font-size-btn:disabled:hover {
    transform: none;
    box-shadow: none;
}

/* Font size button icons */
.map-font-size-btn svg {
    width: 14px;
    height: 14px;
    flex-shrink: 0;
}

/* Specific button styling */
.map-font-size-decrease {
    background: rgba(239, 68, 68, 0.1);
    border-color: rgba(239, 68, 68, 0.3);
    color: #dc2626;
}

.map-font-size-decrease:hover:not(:disabled) {
    background: #dc2626;
    color: white;
    border-color: #dc2626;
}

.map-font-size-increase {
    background: rgba(16, 185, 129, 0.1);
    border-color: rgba(16, 185, 129, 0.3);
    color: #059669;
}

.map-font-size-increase:hover:not(:disabled) {
    background: #059669;
    color: white;
    border-color: #059669;
}

/* ===== PREMIUM FONT SIZE RESET BUTTON REDESIGN ===== */

/* Enhanced reset button with sophisticated styling */
.map-font-size-reset {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    width: 100%;
    padding: 14px var(--map-space-5);
    margin-top: var(--map-space-4);
    margin-bottom: var(--map-space-2);
    background: linear-gradient(135deg,
        var(--map-primary) 0%,
        var(--map-primary-light) 100%);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 14px;
    color: var(--map-white);
    font-size: 12px;
    font-weight: 700;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    text-transform: uppercase;
    letter-spacing: 0.8px;
    position: relative;
    overflow: hidden;
    backdrop-filter: blur(12px);
    -webkit-backdrop-filter: blur(12px);
    box-shadow:
        0 4px 16px rgba(var(--map-primary-rgb), 0.25),
        0 2px 4px rgba(0, 0, 0, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

/* Premium reset button effects and animations */
.map-font-size-reset::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg,
        transparent 0%,
        rgba(255, 255, 255, 0.3) 50%,
        transparent 100%);
    transition: left 0.6s ease;
}

/* Enhanced hover effects */
.map-font-size-reset:hover:not(:disabled) {
    background: linear-gradient(135deg,
        var(--map-primary-dark) 0%,
        var(--map-primary) 100%);
    color: var(--map-white);
    border-color: rgba(255, 255, 255, 0.2);
    transform: translateY(-2px) scale(1.02);
    box-shadow:
        0 8px 24px rgba(var(--map-primary-rgb), 0.35),
        0 4px 8px rgba(0, 0, 0, 0.15),
        inset 0 1px 0 rgba(255, 255, 255, 0.3);
}

.map-font-size-reset:hover:not(:disabled)::before {
    left: 100%;
}

/* Active state */
.map-font-size-reset:active {
    transform: translateY(-1px) scale(1.01);
    box-shadow:
        0 4px 12px rgba(var(--map-primary-rgb), 0.3),
        0 2px 4px rgba(0, 0, 0, 0.1);
}

/* Enhanced icon styling */
.map-font-size-reset svg {
    width: 14px;
    height: 14px;
    flex-shrink: 0;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.1));
}

.map-font-size-reset:hover svg {
    transform: rotate(180deg) scale(1.15);
    filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.2));
}

/* ===== DARK MODE SUPPORT FOR PREMIUM FONT SIZE ===== */

/* Dark mode control group styling */
.map-accessibility-widget.map-dark-mode .map-size-control-group {
    background: linear-gradient(135deg,
        rgba(30, 41, 59, 0.95) 0%,
        rgba(51, 65, 85, 0.9) 50%,
        rgba(71, 85, 105, 0.85) 100%);
    border-color: rgba(139, 92, 246, 0.15);
    box-shadow:
        0 4px 20px rgba(0, 0, 0, 0.2),
        0 1px 3px rgba(0, 0, 0, 0.3),
        inset 0 1px 0 rgba(255, 255, 255, 0.05);
}

/* Dark mode accent line */
.map-accessibility-widget.map-dark-mode .map-size-control-group::before {
    background: linear-gradient(90deg,
        transparent 0%,
        rgba(139, 92, 246, 0.2) 20%,
        rgba(168, 85, 247, 0.4) 50%,
        rgba(139, 92, 246, 0.2) 80%,
        transparent 100%);
}

/* Dark mode inner glow */
.map-accessibility-widget.map-dark-mode .map-size-control-group::after {
    background: linear-gradient(135deg,
        rgba(255, 255, 255, 0.02) 0%,
        rgba(255, 255, 255, 0.01) 50%,
        rgba(255, 255, 255, 0.005) 100%);
}

/* Dark mode control buttons */
.map-accessibility-widget.map-dark-mode .map-size-control-btn {
    background: linear-gradient(135deg,
        #1e293b 0%,
        #334155 50%,
        #475569 100%);
    border-color: #a855f7;
    color: #a855f7;
    box-shadow:
        0 4px 12px rgba(168, 85, 247, 0.2),
        0 2px 4px rgba(0, 0, 0, 0.3),
        inset 0 1px 0 rgba(255, 255, 255, 0.05);
}

.map-accessibility-widget.map-dark-mode .map-size-control-btn:hover:not(:disabled) {
    background: linear-gradient(135deg,
        #8b5cf6 0%,
        #a855f7 100%);
    border-color: #c084fc;
    color: #ffffff;
    box-shadow:
        0 6px 20px rgba(192, 132, 252, 0.4),
        0 3px 8px rgba(0, 0, 0, 0.4),
        inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

/* Dark mode size indicator */
.map-accessibility-widget.map-dark-mode .map-size-indicator {
    background: linear-gradient(135deg,
        rgba(139, 92, 246, 0.12) 0%,
        rgba(168, 85, 247, 0.15) 50%,
        rgba(139, 92, 246, 0.08) 100%);
    border-color: rgba(168, 85, 247, 0.3);
    box-shadow:
        0 2px 8px rgba(139, 92, 246, 0.2),
        inset 0 1px 0 rgba(255, 255, 255, 0.05);
}

/* Dark mode preview text */
.map-accessibility-widget.map-dark-mode .map-size-preview {
    color: #a855f7;
    text-shadow: 0 1px 2px rgba(168, 85, 247, 0.2);
}

/* Dark mode percentage badge */
.map-accessibility-widget.map-dark-mode .map-size-percentage {
    background: linear-gradient(135deg,
        #8b5cf6 0%,
        #a855f7 100%);
    color: #ffffff;
    border-color: rgba(255, 255, 255, 0.1);
    box-shadow:
        0 2px 6px rgba(139, 92, 246, 0.3),
        inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

/* Dark mode reset button */
.map-accessibility-widget.map-dark-mode .map-font-size-reset {
    background: linear-gradient(135deg,
        #8b5cf6 0%,
        #a855f7 50%,
        #c084fc 100%);
    border-color: rgba(255, 255, 255, 0.1);
    color: #ffffff;
    box-shadow:
        0 4px 16px rgba(139, 92, 246, 0.3),
        0 2px 4px rgba(0, 0, 0, 0.3),
        inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

.map-accessibility-widget.map-dark-mode .map-font-size-reset:hover:not(:disabled) {
    background: linear-gradient(135deg,
        #7c3aed 0%,
        #9333ea 50%,
        #a855f7 100%);
    border-color: rgba(255, 255, 255, 0.2);
    box-shadow:
        0 8px 24px rgba(139, 92, 246, 0.4),
        0 4px 8px rgba(0, 0, 0, 0.4),
        inset 0 1px 0 rgba(255, 255, 255, 0.15);
}
/* ===== LINE SPACING CONTROL FEATURE ===== */

/**
 * Line Spacing Control Section
 * Clean, organized layout for line spacing adjustment controls
 */

/* ===== PREMIUM LINE SPACING CONTROLS REDESIGN ===== */
/* Main container with premium styling */
.map-line-spacing-controls {
    display: flex;
    flex-direction: column;
    gap: var(--map-space-4);
    padding: var(--map-space-1);
}

/* Premium control group with sophisticated design */
.map-spacing-control-group {
    display: flex;
    flex-direction: column;
    gap: var(--map-space-4);
    padding: var(--map-space-5);
    background: linear-gradient(135deg,
        rgba(255, 255, 255, 0.95) 0%,
        rgba(248, 250, 252, 0.9) 50%,
        rgba(241, 245, 249, 0.85) 100%);
    border: 1px solid rgba(99, 102, 241, 0.08);
    border-radius: 16px;
    box-shadow:
        0 4px 20px rgba(0, 0, 0, 0.04),
        0 1px 3px rgba(0, 0, 0, 0.06),
        inset 0 1px 0 rgba(255, 255, 255, 0.7);
    position: relative;
    overflow: hidden;
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
}

/* Premium accent line with sophisticated gradient */
.map-spacing-control-group::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: linear-gradient(90deg,
        transparent 0%,
        rgba(99, 102, 241, 0.1) 20%,
        rgba(139, 92, 246, 0.3) 50%,
        rgba(99, 102, 241, 0.1) 80%,
        transparent 100%);
    border-radius: 16px 16px 0 0;
}

/* Subtle inner glow effect */
.map-spacing-control-group::after {
    content: '';
    position: absolute;
    top: 2px;
    left: 1px;
    right: 1px;
    bottom: 1px;
    background: linear-gradient(135deg,
        rgba(255, 255, 255, 0.4) 0%,
        rgba(255, 255, 255, 0.1) 50%,
        rgba(255, 255, 255, 0.05) 100%);
    border-radius: 15px;
    pointer-events: none;
    opacity: 0.6;
}

/* Premium label styling with sophisticated typography */
.map-spacing-labels {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--map-space-4);
    padding: 0 6px;
    position: relative;
    z-index: 2;
}

/* Enhanced label badges with premium styling - Light mode */
.map-spacing-label-min,
.map-spacing-label-center,
.map-spacing-label-max {
    font-size: 9px;
    font-weight: 700;
    color: var(--map-gray-600);
    text-transform: uppercase;
    letter-spacing: 1px;
    min-width: 32px;
    text-align: center;
    background: linear-gradient(135deg,
        rgba(var(--map-primary-rgb), 0.06) 0%,
        rgba(var(--map-primary-rgb), 0.04) 100%);
    padding: 3px 8px;
    border-radius: 6px;
    border: 1px solid rgba(var(--map-primary-rgb), 0.12);
    box-shadow:
        0 1px 2px rgba(0, 0, 0, 0.04),
        inset 0 1px 0 rgba(255, 255, 255, 0.5);
    transition: all 0.2s ease;
    position: relative;
}

/* Special styling for center label (NORMAL) - Light mode */
.map-spacing-label-center {
    color: var(--map-primary);
    background: linear-gradient(135deg,
        rgba(var(--map-primary-rgb), 0.12) 0%,
        rgba(var(--map-primary-rgb), 0.08) 100%);
    border-color: rgba(var(--map-primary-rgb), 0.2);
    font-weight: 800;
    box-shadow:
        0 2px 4px rgba(var(--map-primary-rgb), 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.6);
}

/* ===== PREMIUM SLIDER REDESIGN ===== */

/* Enhanced slider container with premium styling */
.map-slider-container {
    position: relative;
    width: 100%;
    height: 44px;
    display: flex;
    align-items: center;
    padding: 0 2px;
    margin: var(--map-space-2) 0;
}

/* Premium slider with sophisticated design */
.map-premium-slider {
    -webkit-appearance: none;
    appearance: none;
    width: 100%;
    height: 8px;
    background: transparent;
    outline: none;
    border-radius: 6px;
    position: relative;
    z-index: 2;
    cursor: pointer;
    transition: all 0.3s ease;
}

/* Premium slider track - Light mode default */
.map-slider-track {
    position: absolute;
    top: 50%;
    left: 0;
    right: 0;
    height: 8px;
    background: linear-gradient(135deg,
        var(--map-gray-200) 0%,
        var(--map-gray-300) 50%,
        var(--map-gray-400) 100%);
    border-radius: 6px;
    transform: translateY(-50%);
    z-index: 1;
    box-shadow:
        inset 0 2px 4px rgba(0, 0, 0, 0.06),
        0 1px 2px rgba(0, 0, 0, 0.03);
    border: 1px solid var(--map-gray-300);
}

/* Premium progress indicator - Light mode default */
.map-slider-progress {
    height: 100%;
    background: linear-gradient(135deg,
        var(--map-primary) 0%,
        var(--map-primary-light) 100%);
    border-radius: 6px;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    width: 33.33%; /* Default position for 1.5 value */
    box-shadow:
        0 2px 6px rgba(var(--map-primary-rgb), 0.25),
        inset 0 1px 0 rgba(255, 255, 255, 0.2);
    position: relative;
}

/* Subtle glow effect on progress - Light mode */
.map-slider-progress::after {
    content: '';
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    background: linear-gradient(135deg,
        rgba(var(--map-primary-rgb), 0.15) 0%,
        rgba(var(--map-primary-rgb), 0.1) 50%,
        rgba(var(--map-primary-rgb), 0.05) 100%);
    border-radius: 8px;
    z-index: -1;
    opacity: 0;
    transition: opacity 0.3s ease;
}

/* ===== PREMIUM SLIDER THUMB REDESIGN ===== */

/* Enhanced Webkit Slider Thumb - Light mode default */
.map-premium-slider::-webkit-slider-thumb {
    -webkit-appearance: none;
    appearance: none;
    width: 24px;
    height: 24px;
    background: linear-gradient(135deg,
        var(--map-white) 0%,
        var(--map-gray-50) 50%,
        var(--map-gray-100) 100%);
    border: 3px solid var(--map-primary);
    border-radius: 50%;
    cursor: pointer;
    box-shadow:
        0 4px 12px rgba(var(--map-primary-rgb), 0.25),
        0 2px 4px rgba(0, 0, 0, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.8);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
}

/* Premium hover effects - Light mode */
.map-premium-slider::-webkit-slider-thumb:hover {
    transform: scale(1.15);
    background: linear-gradient(135deg,
        var(--map-white) 0%,
        var(--map-gray-50) 50%,
        var(--map-gray-100) 100%);
    border-color: var(--map-primary-dark);
    box-shadow:
        0 6px 20px rgba(var(--map-primary-rgb), 0.4),
        0 3px 8px rgba(0, 0, 0, 0.15),
        inset 0 1px 0 rgba(255, 255, 255, 0.9);
}

/* Active state - Light mode */
.map-premium-slider::-webkit-slider-thumb:active {
    transform: scale(1.05);
    background: linear-gradient(135deg,
        var(--map-gray-50) 0%,
        var(--map-gray-100) 50%,
        var(--map-gray-200) 100%);
    border-color: var(--map-primary-dark);
    box-shadow:
        0 2px 8px rgba(var(--map-primary-rgb), 0.3),
        inset 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* Firefox Slider Thumb */
.map-premium-slider::-moz-range-thumb {
    width: 20px;
    height: 20px;
    background: linear-gradient(135deg, var(--map-white) 0%, var(--map-gray-50) 100%);
    border: 3px solid var(--map-primary);
    border-radius: 50%;
    cursor: pointer;
    box-shadow: 0 2px 6px rgba(var(--map-primary-rgb), 0.3);
    transition: all var(--map-transition-base);
}

.map-premium-slider::-moz-range-track {
    background: transparent;
    border: none;
}

/* ===== PREMIUM VALUE DISPLAY REDESIGN ===== */

/* Enhanced value display with sophisticated styling */
.map-spacing-value {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-top: var(--map-space-4);
    position: relative;
    z-index: 2;
}

/* Premium value badge - Light mode default */
#map-spacing-numeric {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    min-width: 48px;
    height: 32px;
    background: linear-gradient(135deg,
        var(--map-primary) 0%,
        var(--map-primary-light) 100%);
    color: var(--map-white);
    font-size: 13px;
    font-weight: 700;
    letter-spacing: 0.5px;
    border-radius: 12px;
    padding: 0 12px;
    box-shadow:
        0 4px 12px rgba(var(--map-primary-rgb), 0.3),
        0 2px 4px rgba(0, 0, 0, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.2);
    border: 1px solid rgba(255, 255, 255, 0.1);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
}

/* Subtle shine effect */
#map-spacing-numeric::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg,
        transparent 0%,
        rgba(255, 255, 255, 0.2) 50%,
        transparent 100%);
    transition: left 0.6s ease;
}

/* Hover effects for value badge - Light mode */
#map-spacing-numeric:hover {
    transform: translateY(-1px) scale(1.05);
    box-shadow:
        0 6px 20px rgba(var(--map-primary-rgb), 0.4),
        0 3px 8px rgba(0, 0, 0, 0.15),
        inset 0 1px 0 rgba(255, 255, 255, 0.3);
}

#map-spacing-numeric:hover::before {
    left: 100%;
}

.map-spacing-value span {
    font-size: var(--map-font-size-sm);
    font-weight: 700;
    color: var(--map-white);
    background: linear-gradient(135deg, var(--map-primary) 0%, var(--map-primary-light) 100%);
    padding: 6px 16px;
    border-radius: var(--map-radius-lg);
    border: 1px solid rgba(var(--map-primary-rgb), 0.3);
    box-shadow: 0 2px 6px rgba(var(--map-primary-rgb), 0.25);
    min-width: 50px;
    text-align: center;
    letter-spacing: 0.5px;
}

/* ===== PREMIUM RESET BUTTON REDESIGN ===== */

/* Enhanced reset button - Light mode default */
.map-control-reset {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    width: 100%;
    padding: 14px var(--map-space-5);
    margin-top: var(--map-space-4);
    margin-bottom: var(--map-space-2);
    background: linear-gradient(135deg,
        var(--map-primary) 0%,
        var(--map-primary-light) 100%);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 14px;
    color: var(--map-white);
    font-size: 12px;
    font-weight: 700;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    text-transform: uppercase;
    letter-spacing: 0.8px;
    position: relative;
    overflow: hidden;
    backdrop-filter: blur(12px);
    -webkit-backdrop-filter: blur(12px);
    box-shadow:
        0 4px 16px rgba(var(--map-primary-rgb), 0.25),
        0 2px 4px rgba(0, 0, 0, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

/* Premium reset button effects and animations */
.map-control-reset::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg,
        transparent 0%,
        rgba(255, 255, 255, 0.3) 50%,
        transparent 100%);
    transition: left 0.6s ease;
}

/* Enhanced hover effects - Light mode */
.map-control-reset:hover {
    background: linear-gradient(135deg,
        var(--map-primary-dark) 0%,
        var(--map-primary) 100%);
    color: var(--map-white);
    border-color: rgba(255, 255, 255, 0.2);
    transform: translateY(-2px) scale(1.02);
    box-shadow:
        0 8px 24px rgba(var(--map-primary-rgb), 0.35),
        0 4px 8px rgba(0, 0, 0, 0.15),
        inset 0 1px 0 rgba(255, 255, 255, 0.3);
}

.map-control-reset:hover::before {
    left: 100%;
}

/* Active state - Light mode */
.map-control-reset:active {
    transform: translateY(-1px) scale(1.01);
    box-shadow:
        0 4px 12px rgba(var(--map-primary-rgb), 0.3),
        0 2px 4px rgba(0, 0, 0, 0.1);
}

/* Enhanced icon styling */
.map-control-reset svg {
    width: 14px;
    height: 14px;
    flex-shrink: 0;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.1));
}

.map-control-reset:hover svg {
    transform: rotate(180deg) scale(1.15);
    filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.2));
}

/* ===== DARK MODE SUPPORT FOR PREMIUM LINE SPACING ===== */

/* Dark mode control group styling */
.map-accessibility-widget.map-dark-mode .map-spacing-control-group {
    background: linear-gradient(135deg,
        rgba(30, 41, 59, 0.95) 0%,
        rgba(51, 65, 85, 0.9) 50%,
        rgba(71, 85, 105, 0.85) 100%);
    border-color: rgba(139, 92, 246, 0.15);
    box-shadow:
        0 4px 20px rgba(0, 0, 0, 0.2),
        0 1px 3px rgba(0, 0, 0, 0.3),
        inset 0 1px 0 rgba(255, 255, 255, 0.05);
}

/* Dark mode accent line */
.map-accessibility-widget.map-dark-mode .map-spacing-control-group::before {
    background: linear-gradient(90deg,
        transparent 0%,
        rgba(139, 92, 246, 0.2) 20%,
        rgba(168, 85, 247, 0.4) 50%,
        rgba(139, 92, 246, 0.2) 80%,
        transparent 100%);
}

/* Dark mode inner glow */
.map-accessibility-widget.map-dark-mode .map-spacing-control-group::after {
    background: linear-gradient(135deg,
        rgba(255, 255, 255, 0.02) 0%,
        rgba(255, 255, 255, 0.01) 50%,
        rgba(255, 255, 255, 0.005) 100%);
}

/* Dark mode labels */
.map-accessibility-widget.map-dark-mode .map-spacing-label-min,
.map-accessibility-widget.map-dark-mode .map-spacing-label-center,
.map-accessibility-widget.map-dark-mode .map-spacing-label-max {
    color: rgba(255, 255, 255, 0.8);
    background: linear-gradient(135deg,
        rgba(139, 92, 246, 0.1) 0%,
        rgba(168, 85, 247, 0.08) 100%);
    border-color: rgba(139, 92, 246, 0.2);
    box-shadow:
        0 1px 3px rgba(0, 0, 0, 0.2),
        inset 0 1px 0 rgba(255, 255, 255, 0.05);
}

/* Dark mode center label */
.map-accessibility-widget.map-dark-mode .map-spacing-label-center {
    color: #a855f7;
    background: linear-gradient(135deg,
        rgba(139, 92, 246, 0.15) 0%,
        rgba(168, 85, 247, 0.12) 100%);
    border-color: rgba(168, 85, 247, 0.3);
}

/* Dark mode slider track */
.map-accessibility-widget.map-dark-mode .map-slider-track {
    background: linear-gradient(135deg,
        #334155 0%,
        #475569 50%,
        #64748b 100%);
    border-color: rgba(100, 116, 139, 0.3);
    box-shadow:
        inset 0 2px 4px rgba(0, 0, 0, 0.3),
        0 1px 2px rgba(0, 0, 0, 0.2);
}

/* Dark mode slider progress */
.map-accessibility-widget.map-dark-mode .map-slider-progress {
    background: linear-gradient(135deg,
        #8b5cf6 0%,
        #a855f7 50%,
        #c084fc 100%);
    box-shadow:
        0 2px 8px rgba(139, 92, 246, 0.4),
        inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

/* Dark mode slider thumb */
.map-accessibility-widget.map-dark-mode .map-premium-slider::-webkit-slider-thumb {
    background: linear-gradient(135deg,
        #1e293b 0%,
        #334155 50%,
        #475569 100%);
    border-color: #a855f7;
    box-shadow:
        0 4px 12px rgba(168, 85, 247, 0.4),
        0 2px 4px rgba(0, 0, 0, 0.3),
        inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

.map-accessibility-widget.map-dark-mode .map-premium-slider::-webkit-slider-thumb:hover {
    background: linear-gradient(135deg,
        #0f172a 0%,
        #1e293b 50%,
        #334155 100%);
    border-color: #c084fc;
    box-shadow:
        0 6px 20px rgba(192, 132, 252, 0.5),
        0 3px 8px rgba(0, 0, 0, 0.4),
        inset 0 1px 0 rgba(255, 255, 255, 0.15);
}

/* Dark mode value badge */
.map-accessibility-widget.map-dark-mode #map-spacing-numeric {
    background: linear-gradient(135deg,
        #8b5cf6 0%,
        #a855f7 50%,
        #c084fc 100%);
    color: #ffffff;
    border-color: rgba(255, 255, 255, 0.1);
    box-shadow:
        0 4px 12px rgba(139, 92, 246, 0.4),
        0 2px 4px rgba(0, 0, 0, 0.3),
        inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

.map-accessibility-widget.map-dark-mode #map-spacing-numeric:hover {
    box-shadow:
        0 6px 20px rgba(139, 92, 246, 0.5),
        0 3px 8px rgba(0, 0, 0, 0.4),
        inset 0 1px 0 rgba(255, 255, 255, 0.15);
}

/* Dark mode reset button */
.map-accessibility-widget.map-dark-mode .map-control-reset {
    background: linear-gradient(135deg,
        #8b5cf6 0%,
        #a855f7 50%,
        #c084fc 100%);
    border-color: rgba(255, 255, 255, 0.1);
    color: #ffffff;
    box-shadow:
        0 4px 16px rgba(139, 92, 246, 0.3),
        0 2px 4px rgba(0, 0, 0, 0.3),
        inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

.map-accessibility-widget.map-dark-mode .map-control-reset:hover {
    background: linear-gradient(135deg,
        #7c3aed 0%,
        #9333ea 50%,
        #a855f7 100%);
    border-color: rgba(255, 255, 255, 0.2);
    box-shadow:
        0 8px 24px rgba(139, 92, 246, 0.4),
        0 4px 8px rgba(0, 0, 0, 0.4),
        inset 0 1px 0 rgba(255, 255, 255, 0.15);
}

.map-line-spacing-slider-wrapper {
    display: flex;
    align-items: center;
    gap: 10px;
    flex: 1;
}

.map-slider-container {
    flex: 1;
    position: relative;
}

.map-spacing-label-min,
.map-spacing-label-max {
    font-size: 11px;
    color: var(--map-text-secondary);
    font-weight: 500;
    min-width: 30px;
    text-align: center;
    line-height: 1;
}

.map-line-spacing-slider {
    width: 100%;
    height: 4px;
    background: var(--map-border);
    border-radius: 2px;
    outline: none;
    cursor: pointer;
    -webkit-appearance: none;
    appearance: none;
    margin: 0;
    position: relative;
}

.map-line-spacing-slider:focus {
    outline: 2px solid var(--map-primary);
    outline-offset: 2px;
}

.map-line-spacing-slider::-webkit-slider-thumb {
    -webkit-appearance: none;
    appearance: none;
    width: 16px;
    height: 16px;
    background: var(--map-primary);
    border-radius: 50%;
    cursor: pointer;
    transition: all 0.2s ease;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
    border: 2px solid white;
}

.map-line-spacing-slider::-webkit-slider-thumb:hover {
    background: var(--map-primary-dark);
    transform: scale(1.1);
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.25);
}

.map-line-spacing-slider::-moz-range-thumb {
    width: 16px;
    height: 16px;
    background: var(--map-primary);
    border-radius: 50%;
    cursor: pointer;
    border: 2px solid white;
    transition: all 0.2s ease;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
}

.map-line-spacing-slider::-moz-range-thumb:hover {
    background: var(--map-primary-dark);
    transform: scale(1.1);
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.25);
}

.map-line-spacing-slider::-moz-range-track {
    height: 4px;
    background: var(--map-border);
    border-radius: 2px;
    border: none;
}

/* Premium Line Spacing Reset Button - Blue Gradient Style */
.map-line-spacing-reset-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 4px;
    padding: 6px 10px;
    background: linear-gradient(135deg, var(--map-primary) 0%, var(--map-primary-light) 100%);
    border: 2px solid var(--map-primary);
    border-radius: var(--map-radius-md);
    color: var(--map-white);
    font-size: 12px;
    font-weight: 600;
    cursor: pointer;
    transition: all var(--map-transition-base);
    height: 28px;
    min-width: 60px;
    flex-shrink: 0;
    box-shadow: 0 2px 6px rgba(99, 102, 241, 0.15);
    position: relative;
    overflow: hidden;
}

.map-line-spacing-reset-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(255,255,255,0.2) 0%, rgba(255,255,255,0.05) 100%);
    opacity: 0;
    transition: opacity var(--map-transition-base);
}

.map-line-spacing-reset-btn:hover:not(:disabled) {
    background: linear-gradient(135deg, var(--map-primary-dark) 0%, var(--map-primary) 100%);
    color: var(--map-white);
    border-color: var(--map-primary-dark);
    transform: translateY(-2px);
    box-shadow: 0 6px 12px rgba(99, 102, 241, 0.25);
}

.map-line-spacing-reset-btn:hover:not(:disabled)::before {
    opacity: 1;
}

.map-line-spacing-reset-btn:active {
    transform: translateY(0);
}

.map-line-spacing-reset-btn svg {
    width: 12px;
    height: 12px;
    flex-shrink: 0;
}

/* Line spacing scaling system */
.map-line-spacing-active {
    /* This class is used as a selector for dynamic CSS rules */
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .map-feature-controls {
        padding: var(--map-space-4);
    }

    .map-control-header {
        flex-direction: column;
        align-items: flex-start;
        gap: var(--map-space-2);
    }

    /* Language options responsive */
    .map-language-option {
        padding: var(--map-space-3);
    }

    .map-language-info {
        gap: var(--map-space-2);
    }

    .map-language-flag {
        font-size: 20px;
    }

    .map-language-name {
        font-size: 14px;
    }

    .map-language-native {
        font-size: 12px;
    }

    .map-language-check {
        width: 20px;
        height: 20px;
    }

    /* Menu position grid responsive */
    .map-position-grid {
        gap: var(--map-space-2);
    }

    .map-control-value {
        align-self: flex-end;
        min-width: 70px;
    }

    .map-size-control-group {
        flex-direction: column;
        gap: var(--map-space-2);
    }

    .map-size-control-btn {
        width: 100%;
        height: 40px;
        flex-direction: row;
        gap: 6px;
    }

    .map-size-indicator {
        width: 100%;
        flex-direction: row;
        justify-content: space-between;
        padding: 8px;
    }

    .map-spacing-control-group {
        gap: var(--map-space-2);
    }

    .map-premium-slider::-webkit-slider-thumb {
        width: 18px;
        height: 18px;
    }

    .map-premium-slider::-moz-range-thumb {
        width: 18px;
        height: 18px;
    }

    .map-line-spacing-controls {
        gap: 6px;
    }

    .map-line-spacing-slider-wrapper {
        gap: 8px;
    }

    .map-line-spacing-reset-btn {
        min-width: 50px;
        padding: 5px 8px;
        font-size: 11px;
        height: 26px;
    }

    .map-line-spacing-reset-btn svg {
        width: 10px;
        height: 10px;
    }

    .map-spacing-label-min,
    .map-spacing-label-max {
        font-size: 10px;
        min-width: 25px;
    }

    .map-line-spacing-slider::-webkit-slider-thumb {
        width: 14px;
        height: 14px;
    }

    .map-line-spacing-slider::-moz-range-thumb {
        width: 14px;
        height: 14px;
    }
}
