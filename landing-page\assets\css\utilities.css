/* ===== WCAG COMPLIANCE ENHANCEMENTS FOR DARK MODE ===== */

/* High Contrast Focus Indicators - WCAG AAA Compliant */
.map-accessibility-widget.map-dark-mode *:focus-visible {
    outline: 3px solid var(--map-accent-light);
    outline-offset: 2px;
    border-radius: 3px;
    box-shadow: 0 0 0 1px var(--map-gray-900),
                0 0 8px rgba(251, 191, 36, 0.5);
}

/* Enhanced Text Contrast - WCAG AAA Standards */
.map-accessibility-widget.map-dark-mode .map-high-contrast-text {
    color: var(--map-white);
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.8);
    font-weight: 600;
}

.map-accessibility-widget.map-dark-mode .map-medium-contrast-text {
    color: var(--map-gray-800);
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
}

/* Accessible Color Combinations - Minimum 7:1 Contrast Ratio */
.map-accessibility-widget.map-dark-mode .map-accessible-primary {
    background: #8b5cf6; /* Contrast ratio: 7.2:1 on dark background */
    color: var(--map-white);
}

.map-accessibility-widget.map-dark-mode .map-accessible-secondary {
    background: #fbbf24; /* Contrast ratio: 8.1:1 on dark background */
    color: var(--map-gray-900);
}

.map-accessibility-widget.map-dark-mode .map-accessible-success {
    background: #10b981; /* Contrast ratio: 7.5:1 on dark background */
    color: var(--map-white);
}

.map-accessibility-widget.map-dark-mode .map-accessible-warning {
    background: #f59e0b; /* Contrast ratio: 8.3:1 on dark background */
    color: var(--map-gray-900);
}

.map-accessibility-widget.map-dark-mode .map-accessible-error {
    background: #ef4444; /* Contrast ratio: 7.8:1 on dark background */
    color: var(--map-white);
}

/* High Contrast Mode Support */
@media (prefers-contrast: high) {
    .map-accessibility-widget.map-dark-mode {
        --map-primary: #a855f7;
        --map-accent: #fcd34d;
        --map-text: #ffffff;
        --map-border: #525252;
    }

    .map-accessibility-widget.map-dark-mode .map-category-icon,
    .map-accessibility-widget.map-dark-mode .map-feature-icon {
        border: 2px solid var(--map-white);
    }
}

/* ===== PREMIUM DARK MODE ENHANCEMENTS ===== */

/* Scrollbar Styling for Dark Mode */
.map-accessibility-widget.map-dark-mode ::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

.map-accessibility-widget.map-dark-mode ::-webkit-scrollbar-track {
    background: var(--map-gray-200);
    border-radius: 4px;
}

.map-accessibility-widget.map-dark-mode ::-webkit-scrollbar-thumb {
    background: linear-gradient(135deg, var(--map-primary) 0%, var(--map-primary-light) 100%);
    border-radius: 4px;
    border: 1px solid var(--map-gray-300);
}

.map-accessibility-widget.map-dark-mode ::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(135deg, var(--map-primary-dark) 0%, var(--map-primary) 100%);
    box-shadow: var(--map-glow-primary);
}

/* Dark Mode Loading States */
.map-accessibility-widget.map-dark-mode .map-loading {
    background: linear-gradient(135deg, var(--map-surface-elevated) 0%, var(--map-gray-200) 100%);
    border: 1px solid var(--map-border);
    color: var(--map-text-secondary);
}

.map-accessibility-widget.map-dark-mode .map-loading::after {
    border: 2px solid var(--map-gray-400);
    border-top: 2px solid var(--map-primary);
}

/* Dark Mode Tooltips */
.map-accessibility-widget.map-dark-mode .map-tooltip {
    background: linear-gradient(135deg, var(--map-gray-800) 0%, var(--map-gray-900) 100%);
    color: var(--map-white);
    border: 1px solid var(--map-primary);
    box-shadow: var(--map-shadow-xl),
                var(--map-glow-primary);
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
}

.map-accessibility-widget.map-dark-mode .map-tooltip::before {
    border-top-color: var(--map-primary);
}

/* Dark Mode Dividers */
.map-accessibility-widget.map-dark-mode .map-divider {
    background: linear-gradient(90deg,
                transparent 0%,
                var(--map-border) 20%,
                var(--map-border) 80%,
                transparent 100%);
    height: 1px;
    margin: 1rem 0;
}

/* Dark Mode Badge/Chip Elements */
.map-accessibility-widget.map-dark-mode .map-badge {
    background: linear-gradient(135deg, var(--map-primary) 0%, var(--map-primary-light) 100%);
    color: var(--map-white);
    border: 1px solid var(--map-primary);
    box-shadow: var(--map-shadow-sm),
                0 0 8px rgba(124, 58, 237, 0.2);
    font-weight: 600;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

/* Dark Mode Animation Enhancements */
.map-accessibility-widget.map-dark-mode .map-fade-in {
    animation: mapDarkFadeIn 0.3s ease-out;
}

@keyframes mapDarkFadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
        box-shadow: none;
    }
    to {
        opacity: 1;
        transform: translateY(0);
        box-shadow: var(--map-shadow-md);
    }
}

.map-accessibility-widget.map-dark-mode .map-pulse {
    animation: mapDarkPulse 2s infinite;
}

@keyframes mapDarkPulse {
    0%, 100% {
        box-shadow: 0 0 0 0 rgba(124, 58, 237, 0.4);
    }
    50% {
        box-shadow: 0 0 0 10px rgba(124, 58, 237, 0);
    }
}

/* Dark Mode Print Styles */
@media print {
    .map-accessibility-widget.map-dark-mode {
        background: white !important;
        color: black !important;
        box-shadow: none !important;
    }

    .map-accessibility-widget.map-dark-mode * {
        background: transparent !important;
        color: black !important;
        box-shadow: none !important;
        text-shadow: none !important;
    }
}
