/* ===== COLOR STUDIO - CLEAN DESIGN ===== */

/* Ensure hover effect works properly for Color Studio section */
.map-color-studio {
    position: relative;
}

/* Studio Header - Clean Style */
.map-studio-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: var(--map-space-5);
    background: rgba(var(--map-bg-rgb), 0.3);
    border-radius: var(--map-radius-lg) var(--map-radius-lg) 0 0;
    margin-bottom: 0;
    position: relative;
    z-index: 2;
    border: none;
    cursor: pointer;
    transition: all var(--map-transition-base);
}

.map-custom-theme-info {
    display: flex;
    align-items: center;
    gap: 12px;
    flex: 1;
}

.map-studio-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 36px;
    height: 36px;
    background: var(--map-primary);
    color: var(--map-white);
    border-radius: 6px;
    flex-shrink: 0;
    transition: all var(--map-transition-base);
}

.map-custom-theme-text {
    flex: 1;
}

.map-custom-theme-title {
    font-size: 16px;
    font-weight: 600;
    color: var(--map-gray-800);
    margin-bottom: 2px;
    line-height: 1.4;
}

.map-custom-theme-desc {
    font-size: 14px;
    color: var(--map-gray-500);
    line-height: 1.3;
    margin: 0;
}

.map-studio-arrow {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 20px;
    height: 20px;
    color: var(--map-gray-400);
    transition: all var(--map-transition-base);
    margin-left: var(--map-space-3);
}

.map-studio-header[aria-expanded="true"] .map-studio-arrow {
    transform: rotate(90deg);
}

/* Studio Content */
.map-studio-content {
    padding: var(--map-space-6);
    background: var(--map-white);
    border-radius: 0 0 var(--map-radius-lg) var(--map-radius-lg);
    position: relative;
    z-index: 2;
}

/* ===== PREMIUM COMPACT COLOR STUDIO LAYOUT ===== */

/* Main Container - Matching Feature Controls Pattern */
.map-color-studio-compact {
    display: flex;
    flex-direction: column;
    gap: var(--map-space-3);
    margin-bottom: var(--map-space-4);
}

/* Color Row - Perfect Alignment for Icons and Text */
.map-color-row {
    display: flex;
    align-items: center; /* Center all content vertically */
    justify-content: space-between;
    background: var(--map-white);
    border: 1px solid var(--map-gray-200);
    border-radius: var(--map-radius-lg);
    padding: var(--map-space-4);
    transition: all var(--map-transition-base);
    min-height: 64px; /* Slightly taller for better text alignment */
    box-sizing: border-box;
}

.map-color-row:hover {
    background: linear-gradient(135deg, var(--map-gray-50) 0%, var(--map-white) 100%);
    border-color: var(--map-primary);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(99, 102, 241, 0.1);
}

/* Color Label Section - Perfect Icon and Text Alignment */
.map-color-label {
    display: flex;
    align-items: center;
    flex: 1;
    min-width: 0; /* Prevent flex shrinking issues */
    gap: var(--map-space-3); /* Consistent spacing between icon and text */
}

/* Color Icon - Fixed Width for Consistent Alignment */
.map-color-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 36px;
    height: 36px;
    background: linear-gradient(135deg, var(--map-primary) 0%, var(--map-primary-light) 100%);
    border-radius: var(--map-radius-md);
    color: var(--map-white);
    transition: all var(--map-transition-base);
    box-shadow: 0 2px 4px rgba(99, 102, 241, 0.2);
    flex-shrink: 0;
}

.map-color-row:hover .map-color-icon {
    transform: scale(1.05);
    box-shadow: 0 4px 8px rgba(99, 102, 241, 0.3);
}

/* Color Info - Perfect Text Alignment */
.map-color-info {
    display: flex;
    flex-direction: column;
    justify-content: center; /* Center text vertically relative to icon */
    gap: 2px;
    flex: 1;
    min-width: 0; /* Prevent text overflow issues */
}

.map-color-title {
    font-size: var(--map-font-size-base);
    font-weight: 600;
    color: var(--map-gray-800);
    line-height: 1.2;
    margin: 0;
    white-space: nowrap; /* Prevent title wrapping */
    overflow: hidden;
    text-overflow: ellipsis;
}

.map-color-desc {
    font-size: var(--map-font-size-sm);
    color: var(--map-gray-500);
    line-height: 1.3;
    margin: 0;
    white-space: nowrap; /* Prevent description wrapping */
    overflow: hidden;
    text-overflow: ellipsis;
}

/* Color Controls Section - Fixed Width to Prevent Layout Shifts */
.map-color-controls {
    display: flex;
    align-items: center;
    gap: var(--map-space-2);
    flex-shrink: 0;
    width: 40px; /* Back to original width - only color picker */
    justify-content: flex-end;
    position: relative;
}

/* Color Picker Wrapper - Contains picker and overlay reset button */
.map-color-picker-wrapper {
    position: relative;
    width: 40px;
    height: 40px;
    flex-shrink: 0;
}

/* Compact Color Picker - Fixed Size to Prevent Layout Shifts */
.map-color-picker-compact {
    width: 100%;
    height: 100%;
    border: 3px solid var(--map-primary);
    border-radius: var(--map-radius-md);
    cursor: pointer;
    transition: all var(--map-transition-base);
    background: transparent;
    padding: 0;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(99, 102, 241, 0.2);
    box-sizing: border-box; /* Include border in size calculation */
}

.map-color-picker-compact:hover {
    border-color: var(--map-primary-dark);
    transform: scale(1.05);
    box-shadow: 0 4px 12px rgba(99, 102, 241, 0.3);
}

.map-color-picker-compact:focus-visible {
    outline: none;
    box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.3);
    border-color: var(--map-primary);
}

/* Compact Color Picker Browser Specific Styles - Remove White Space */
.map-color-picker-compact::-webkit-color-swatch-wrapper {
    padding: 0 !important;
    margin: 0 !important;
    border: none !important;
    border-radius: calc(var(--map-radius-md) - 3px) !important;
    width: 100% !important;
    height: 100% !important;
    box-sizing: border-box !important;
}

.map-color-picker-compact::-webkit-color-swatch {
    border: none !important;
    border-radius: calc(var(--map-radius-md) - 3px) !important;
    margin: 0 !important;
    padding: 0 !important;
    width: 100% !important;
    height: 100% !important;
    box-sizing: border-box !important;
}

.map-color-picker-compact::-moz-color-swatch {
    border: none !important;
    border-radius: calc(var(--map-radius-md) - 3px) !important;
    margin: 0 !important;
    padding: 0 !important;
    width: 100% !important;
    height: 100% !important;
    box-sizing: border-box !important;
}

/* Premium Overlay Reset Button - Inside Color Picker */
.map-color-reset-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 18px;
    height: 18px;
    background: rgba(255, 255, 255, 0.95);
    border: 1px solid rgba(239, 68, 68, 0.3);
    border-radius: 50%;
    color: #ef4444;
    cursor: pointer;
    transition: all var(--map-transition-base);
    padding: 0;
    opacity: 0;
    transform: scale(0);
    pointer-events: none;
    box-sizing: border-box;
    position: absolute;
    top: -2px;
    right: -2px;
    z-index: 10;
    backdrop-filter: blur(4px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

.map-color-reset-btn.visible {
    opacity: 1;
    transform: scale(1);
    pointer-events: auto;
}

.map-color-reset-btn:hover {
    background: #ef4444;
    border-color: #ef4444;
    color: var(--map-white);
    transform: scale(1.1);
    box-shadow: 0 4px 12px rgba(239, 68, 68, 0.4);
}

.map-color-reset-btn:active {
    transform: scale(0.9);
}

/* Show reset button on color picker hover */
.map-color-picker-wrapper:hover .map-color-reset-btn.visible {
    opacity: 1;
    transform: scale(1);
}

/* Premium Hover Effects for Color Rows - Enhanced for Clickability */
.map-color-row {
    cursor: pointer;
    transition: all var(--map-transition-base);
}

.map-color-row:hover {
    background: rgba(99, 102, 241, 0.02);
    border-radius: var(--map-radius-md);
    transform: translateY(-1px);
}

.map-color-row:hover .map-color-title {
    color: var(--map-gray-900);
}

.map-color-row:hover .map-color-desc {
    color: var(--map-gray-600);
}

/* Mobile Responsive - Compact Layout */
@media (max-width: 480px) {
    .map-color-row {
        flex-direction: column;
        align-items: stretch;
        gap: var(--map-space-3);
        padding: var(--map-space-3);
        min-height: auto;
    }

    .map-color-label {
        justify-content: flex-start;
    }

    .map-color-controls {
        justify-content: center;
        gap: var(--map-space-3);
    }

    .map-color-picker-compact {
        width: 36px;
        height: 36px;
    }

    .map-color-reset-btn {
        width: 28px;
        height: 28px;
    }
}

/* Color Cards */
.map-color-card {
    background: rgba(255, 255, 255, 0.9);
    border: 1px solid rgba(148, 163, 184, 0.2);
    border-radius: 16px;
    padding: var(--map-space-5);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
    backdrop-filter: blur(8px);
    box-shadow:
        0 1px 3px 0 rgba(0, 0, 0, 0.1),
        0 1px 2px 0 rgba(0, 0, 0, 0.06);
}

.map-color-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, #667eea, #764ba2, #f093fb, #f5576c);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.map-color-card:hover {
    transform: translateY(-4px);
    box-shadow:
        0 10px 25px -3px rgba(0, 0, 0, 0.1),
        0 4px 6px -2px rgba(0, 0, 0, 0.05);
    border-color: rgba(102, 126, 234, 0.3);
}

.map-color-card:hover::before {
    opacity: 1;
}

/* Color Card Header */
.map-color-card-header {
    display: flex;
    align-items: flex-start;
    gap: var(--map-space-3);
    margin-bottom: var(--map-space-4);
}

.map-color-card-icon {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 12px;
    padding: var(--map-space-3);
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 4px 6px -1px rgba(102, 126, 234, 0.3);
    transition: all 0.3s ease;
}

.map-color-card:hover .map-color-card-icon {
    transform: scale(1.1) rotate(5deg);
    box-shadow: 0 8px 15px -3px rgba(102, 126, 234, 0.4);
}

.map-color-card-info {
    flex: 1;
}

.map-color-card-title {
    font-size: 16px;
    font-weight: 600;
    color: var(--map-gray-900);
    margin: 0 0 4px 0;
    line-height: 1.2;
}

.map-color-card-desc {
    font-size: 13px;
    color: var(--map-gray-600);
    margin: 0;
    line-height: 1.4;
}

/* Premium Color Reset Button - Blue Gradient Style */
.map-color-reset {
    background: linear-gradient(135deg, var(--map-primary) 0%, var(--map-primary-light) 100%);
    color: var(--map-white);
    border: 1px solid var(--map-primary);
    border-radius: var(--map-radius-md);
    padding: var(--map-space-2);
    cursor: pointer;
    transition: all var(--map-transition-base);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transform: scale(0.8);
    box-shadow: 0 2px 6px rgba(99, 102, 241, 0.15);
    position: relative;
    overflow: hidden;
    width: 32px;
    height: 32px;
}

.map-color-reset::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(255,255,255,0.2) 0%, rgba(255,255,255,0.05) 100%);
    opacity: 0;
    transition: opacity var(--map-transition-base);
}

.map-color-card:hover .map-color-reset {
    opacity: 1;
    transform: scale(1);
}

.map-color-reset:hover {
    background: linear-gradient(135deg, var(--map-primary-dark) 0%, var(--map-primary) 100%);
    color: var(--map-white);
    transform: scale(1.1);
    box-shadow: 0 4px 8px rgba(99, 102, 241, 0.25);
}

.map-color-reset:hover::before {
    opacity: 1;
}

.map-color-reset:active {
    transform: scale(0.95);
    box-shadow: 0 2px 4px rgba(99, 102, 241, 0.2);
}

/* Color Picker Wrapper - Invisible Container (Different context - not used in compact layout) */
.map-studio-color-picker-wrapper {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: var(--map-space-4);
    background: rgba(248, 250, 252, 0.8);
    border: 1px solid rgba(148, 163, 184, 0.2);
    border-radius: 12px;
    transition: all 0.3s ease;
}

.map-studio-color-picker-wrapper:hover {
    background: rgba(248, 250, 252, 1);
    border-color: rgba(102, 126, 234, 0.3);
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

/* Studio Color Picker */
.map-studio-picker {
    width: 60px;
    height: 60px;
    border: 3px solid rgba(255, 255, 255, 0.8);
    border-radius: 16px;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    background: none;
    padding: 0;
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
    box-shadow:
        0 4px 6px -1px rgba(0, 0, 0, 0.1),
        0 2px 4px -1px rgba(0, 0, 0, 0.06),
        inset 0 2px 4px rgba(255, 255, 255, 0.1);
    position: relative;
    overflow: hidden;
}

.map-studio-picker::before {
    content: '';
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    background: linear-gradient(45deg, #667eea, #764ba2, #f093fb, #f5576c);
    border-radius: 18px;
    z-index: -1;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.map-studio-picker:hover::before {
    opacity: 1;
}

.map-studio-picker:hover {
    transform: scale(1.1);
    border-color: rgba(255, 255, 255, 1);
    box-shadow:
        0 10px 15px -3px rgba(0, 0, 0, 0.1),
        0 4px 6px -2px rgba(0, 0, 0, 0.05),
        inset 0 2px 4px rgba(255, 255, 255, 0.2);
}

.map-studio-picker:focus {
    outline: none;
    transform: scale(1.05);
    box-shadow:
        0 0 0 4px rgba(102, 126, 234, 0.3),
        0 10px 15px -3px rgba(0, 0, 0, 0.1),
        0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

.map-studio-picker:active {
    transform: scale(0.95);
}

/* Color Picker Browser Specific Styles */
.map-studio-picker::-webkit-color-swatch-wrapper {
    padding: 0;
    border: none;
    border-radius: 13px;
}

.map-studio-picker::-webkit-color-swatch {
    border: none;
    border-radius: 13px;
}

.map-studio-picker::-moz-color-swatch {
    border: none;
    border-radius: 13px;
}

/* Color Preview - Clean Professional Design */
.map-color-preview {
    flex: 1;
    height: 40px;
    background: rgba(255, 255, 255, 0.9);
    border: 1px solid rgba(148, 163, 184, 0.2);
    border-radius: 8px;
    transition: all 0.3s ease;
    backdrop-filter: blur(4px);
    position: relative;
    overflow: hidden;
}

/* Add subtle pattern for empty state */
.map-color-preview::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg,
        rgba(0,0,0,0.03) 25%,
        transparent 25%,
        transparent 75%,
        rgba(0,0,0,0.03) 75%),
    linear-gradient(45deg,
        rgba(0,0,0,0.03) 25%,
        transparent 25%,
        transparent 75%,
        rgba(0,0,0,0.03) 75%);
    background-size: 8px 8px;
    background-position: 0 0, 4px 4px;
    opacity: 0.5;
}

/* Add subtle inner shadow for depth */
.map-color-preview::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.1);
    border-radius: 7px;
}

/* Enhanced Accessibility */
.map-color-card:focus-within {
    outline: 2px solid #667eea;
    outline-offset: 2px;
}

.map-studio-picker:focus-visible {
    outline: 3px solid #667eea;
    outline-offset: 3px;
}

.map-color-reset:focus-visible {
    outline: 2px solid #dc2626;
    outline-offset: 2px;
}

/* Loading States */
.map-color-card.loading {
    opacity: 0.7;
    pointer-events: none;
}

.map-color-card.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid #667eea;
    border-top: 2px solid transparent;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

.map-collapsible-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    padding: var(--map-space-4) var(--map-space-5);
    background: var(--map-white);
    border: none;
    cursor: pointer;
    transition: all var(--map-transition-base);
    text-align: left;
    position: relative;
    z-index: 1;
    pointer-events: auto;
}

.map-collapsible-header:hover {
    background: var(--map-gray-50);
}

.map-custom-theme-info {
    display: flex;
    align-items: center;
    gap: var(--map-space-3);
}

.map-custom-theme-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    background: linear-gradient(135deg, #8b5cf6 0%, #a78bfa 100%);
    border-radius: var(--map-radius-lg);
    color: var(--map-white);
    flex-shrink: 0;
    box-shadow: var(--map-shadow-sm);
}

.map-custom-theme-emoji {
    font-size: 18px;
}

.map-custom-theme-text {
    flex: 1;
}

.map-custom-theme-title {
    margin: 0 0 var(--map-space-1) 0;
    font-size: var(--map-font-size-base);
    font-weight: 600;
    color: var(--map-gray-800);
    line-height: 1.4;
}

.map-custom-theme-desc {
    font-size: var(--map-font-size-sm);
    color: var(--map-gray-500);
    line-height: 1.3;
    margin: 0;
}

.map-custom-theme-arrow {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 24px;
    height: 24px;
    color: var(--map-gray-400);
    transition: transform var(--map-transition-base);
}

.map-collapsible-header[aria-expanded="true"] .map-custom-theme-arrow {
    transform: rotate(90deg);
}

/* Custom Theme Content */
.map-custom-theme-content {
    padding: var(--map-space-5);
    background: var(--map-white);
    border-radius: 0 0 var(--map-radius-lg) var(--map-radius-lg);
    pointer-events: auto;
    position: relative;
    z-index: 1;
}

/* Color Controls */
.map-custom-theme-controls {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--map-space-3);
    margin-bottom: var(--map-space-6);
}

.map-color-control {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: var(--map-space-2);
    padding: var(--map-space-3);
    background: var(--map-gray-50);
    border: 1px solid var(--map-gray-200);
    border-radius: var(--map-radius-md);
    transition: all var(--map-transition-base);
    pointer-events: auto;
    position: relative;
    text-align: center;
}

.map-color-control:hover {
    border-color: var(--map-primary-light);
    box-shadow: var(--map-shadow-sm);
}

.map-color-label {
    display: flex;
    align-items: center;
    gap: var(--map-space-2);
    font-size: var(--map-font-size-sm);
    font-weight: 500;
    color: var(--map-gray-700);
    cursor: pointer;
    justify-content: center;
}

.map-color-icon {
    font-size: 16px;
}

.map-color-picker {
    width: 60px;
    height: 40px;
    border: none;
    border-radius: var(--map-radius-md);
    cursor: pointer;
    transition: all var(--map-transition-base);
    background: none;
    padding: 0;
    pointer-events: auto;
    position: relative;
    z-index: 2;
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
}

.map-color-picker::-webkit-color-swatch-wrapper {
    padding: 0;
    border: none;
    border-radius: var(--map-radius-md);
}

.map-color-picker::-webkit-color-swatch {
    border: none;
    border-radius: var(--map-radius-md);
}

.map-color-picker::-moz-color-swatch {
    border: none;
    border-radius: var(--map-radius-md);
}

.map-color-picker:hover {
    transform: scale(1.05);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

/* Default subtle focus for color picker */
.map-color-picker:focus {
    outline: 2px solid var(--map-gray-400);
    outline-offset: 1px;
}

/* Custom Theme Applied State */
body.map-theme-custom {
    /* Custom theme styles will be applied dynamically via JavaScript */
}
/* Color Blind Friendly Theme - Uses safe color combinations */
body.map-theme-colorblind {
    background-color: #ffffff !important;
    color: #000000 !important;
}

body.map-theme-colorblind * {
    /* Reset problematic colors to safe alternatives */
    background-color: inherit !important;
    color: inherit !important;
    border-color: #000000 !important;
}

/* Color blind safe link colors - use blue and underlines */
body.map-theme-colorblind a {
    color: #0066cc !important;
    text-decoration: underline !important;
}

body.map-theme-colorblind a:hover {
    color: #004499 !important;
    text-decoration: underline !important;
}

/* ===== COLOR STUDIO ICONS DARK MODE FIXES ===== */

/* Fix Color Studio icons to be white in dark mode - following user preference */
.map-accessibility-widget.map-dark-mode .map-color-icon svg {
    color: #ffffff !important;
    stroke: #ffffff !important;
}

/* Ensure all Color Studio icon paths and elements are white */
.map-accessibility-widget.map-dark-mode .map-color-icon svg path,
.map-accessibility-widget.map-dark-mode .map-color-icon svg circle,
.map-accessibility-widget.map-dark-mode .map-color-icon svg rect,
.map-accessibility-widget.map-dark-mode .map-color-icon svg polyline,
.map-accessibility-widget.map-dark-mode .map-color-icon svg line {
    stroke: #ffffff !important;
    fill: none !important;
}

/* Fix Color Studio container backgrounds for dark mode */
.map-accessibility-widget.map-dark-mode .map-color-row {
    background: var(--map-gray-100);
    border: 1px solid var(--map-border);
}

.map-accessibility-widget.map-dark-mode .map-color-row:hover {
    background: var(--map-gray-200);
    border-color: var(--map-primary);
    box-shadow: 0 4px 8px rgba(124, 58, 237, 0.2);
}

/* Color Studio text styling for dark mode */
.map-accessibility-widget.map-dark-mode .map-color-title {
    color: var(--map-text);
    font-weight: 600;
}

.map-accessibility-widget.map-dark-mode .map-color-desc {
    color: var(--map-text-secondary);
}

/* Color Studio compact layout dark mode */
.map-accessibility-widget.map-dark-mode .map-color-studio-compact {
    background: transparent;
}

/* Fix color reset button positioning in dark mode */
.map-accessibility-widget.map-dark-mode .map-color-reset-btn {
    position: absolute !important;
    top: -2px !important;
    right: -2px !important;
    bottom: auto !important;
    left: auto !important;
    transform: none !important;
}

/* ===== COLORBLIND THEME EXTENDED STYLES ===== */

body.map-theme-colorblind a:visited {
    color: #663399 !important;
    text-decoration: underline !important;
}

/* Safe button colors - high contrast blue and yellow */
body.map-theme-colorblind button,
body.map-theme-colorblind .button,
body.map-theme-colorblind input[type="button"],
body.map-theme-colorblind input[type="submit"] {
    background-color: #0066cc !important;
    color: #ffffff !important;
    border: 2px solid #000000 !important;
}

body.map-theme-colorblind button:hover,
body.map-theme-colorblind .button:hover,
body.map-theme-colorblind input[type="button"]:hover,
body.map-theme-colorblind input[type="submit"]:hover {
    background-color: #ffcc00 !important;
    color: #000000 !important;
    border: 2px solid #000000 !important;
}

/* Form elements with high contrast */
body.map-theme-colorblind input,
body.map-theme-colorblind textarea,
body.map-theme-colorblind select {
    background-color: #ffffff !important;
    color: #000000 !important;
    border: 2px solid #000000 !important;
}

body.map-theme-colorblind input:focus,
body.map-theme-colorblind textarea:focus,
body.map-theme-colorblind select:focus {
    background-color: #ffffcc !important;
    color: #000000 !important;
    border: 3px solid #0066cc !important;
    outline: none !important;
}

/* Safe colors for success/error states */
body.map-theme-colorblind .success,
body.map-theme-colorblind .notice-success {
    background-color: #ffffff !important;
    color: #000000 !important;
    border: 3px solid #000000 !important;
    border-left: 8px solid #000000 !important;
}

body.map-theme-colorblind .error,
body.map-theme-colorblind .notice-error {
    background-color: #000000 !important;
    color: #ffffff !important;
    border: 3px solid #ffffff !important;
}

body.map-theme-colorblind .warning,
body.map-theme-colorblind .notice-warning {
    background-color: #ffcc00 !important;
    color: #000000 !important;
    border: 3px solid #000000 !important;
}

/* Navigation and menu items */
body.map-theme-colorblind nav,
body.map-theme-colorblind .menu,
body.map-theme-colorblind .nav {
    background-color: #f0f0f0 !important;
    border: 2px solid #000000 !important;
}

body.map-theme-colorblind nav a,
body.map-theme-colorblind .menu a,
body.map-theme-colorblind .nav a {
    color: #0066cc !important;
    text-decoration: underline !important;
    background-color: transparent !important;
}

body.map-theme-colorblind nav a:hover,
body.map-theme-colorblind .menu a:hover,
body.map-theme-colorblind .nav a:hover {
    background-color: #ffcc00 !important;
    color: #000000 !important;
}

/* Headers with clear hierarchy */
body.map-theme-colorblind h1,
body.map-theme-colorblind h2,
body.map-theme-colorblind h3,
body.map-theme-colorblind h4,
body.map-theme-colorblind h5,
body.map-theme-colorblind h6 {
    color: #000000 !important;
    background-color: transparent !important;
    border-bottom: 2px solid #000000 !important;
    padding-bottom: 4px !important;
}

/* Tables with clear borders */
body.map-theme-colorblind table {
    border: 2px solid #000000 !important;
    background-color: #ffffff !important;
}

body.map-theme-colorblind th,
body.map-theme-colorblind td {
    border: 1px solid #000000 !important;
    background-color: #ffffff !important;
    color: #000000 !important;
}

body.map-theme-colorblind th {
    background-color: #f0f0f0 !important;
    font-weight: bold !important;
}

/* Ensure images have alt text visibility */
body.map-theme-colorblind img:not([alt]),
body.map-theme-colorblind img[alt=""] {
    border: 3px dashed #ff0000 !important;
    background-color: #ffcccc !important;
}

/* High contrast for accessibility widget itself */
body.map-theme-colorblind .map-accessibility-widget {
    background-color: #ffffff !important;
    border: 3px solid #000000 !important;
}

body.map-theme-colorblind .map-widget-panel {
    background-color: #ffffff !important;
    border: 3px solid #000000 !important;
    color: #000000 !important;
}
