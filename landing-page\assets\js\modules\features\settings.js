/**
 * Settings Module - Settings and Configuration Management
 *
 * This module contains:
 * - Dark mode toggle for widget interface
 * - Language selection and management
 * - Menu position settings
 * - Settings section initialization
 * - General configuration state management
 *
 * @package MyAccessibilityPlugin
 * @since 1.0.0
 */

(function($) {
    'use strict';

    // Extend MapAccessibility with preferences functionality
    if (typeof window.MapAccessibility !== 'undefined') {

        /**
         * Initialize preferences section features
         */
        window.MapAccessibility.prototype.initializePreferencesSection = function() {
            // Language selection toggle
            $('#map-language-toggle').off('click.preferences').on('click.preferences', (e) => {
                e.preventDefault();
                this.toggleLanguageSection();
            });

            // Language option selection
            $('.map-language-option').off('click.preferences').on('click.preferences', (e) => {
                e.preventDefault();
                const $option = $(e.currentTarget);
                const language = $option.data('language');
                this.selectLanguage(language);
            });

            // Menu position toggle
            $('#map-menu-position-toggle').off('click.preferences').on('click.preferences', (e) => {
                e.preventDefault();
                this.toggleMenuPositionSection();
            });

            // Menu position option selection
            $('#map-menu-position-content .map-language-option').off('click.preferences').on('click.preferences', (e) => {
                e.preventDefault();
                const $option = $(e.currentTarget);
                const position = $option.data('position');
                this.selectMenuPosition(position);
            });
        };



        /**
         * Toggle language section visibility
         */
        window.MapAccessibility.prototype.toggleLanguageSection = function() {
            const $toggle = $('#map-language-toggle');
            const $content = $('#map-language-content');
            const isActive = $toggle.attr('data-active') === 'true';

            if (isActive) {
                $content.slideUp(200);
                $toggle.attr('data-active', 'false');
                $toggle.removeClass('active');
                this.toggleNavigationArrow($toggle);
            } else {
                $content.slideDown(200);
                $toggle.attr('data-active', 'true');
                $toggle.addClass('active');
                this.toggleNavigationArrow($toggle);
            }
        };

        /**
         * Select a language
         */
        window.MapAccessibility.prototype.selectLanguage = async function(language) {
            try {
                // Store the selected language and save immediately
                this.currentLanguage = language;

                // Double-save: Direct localStorage save + full preferences save
                localStorage.setItem('map_current_language', language);
                this.saveUserPreferences();

                // Load the language translations if not already loaded
                await this.loadLanguage(language);

                // Apply translations immediately (this will update checkmarks)
                this.applyTranslations();

                // Remove active state from all options
                $('.map-language-option').removeClass('active map-language-active');

                // Add active state to selected option
                $(`.map-language-option[data-language="${language}"]`).addClass('active map-language-active');
            } catch (error) {
                console.error('Failed to load language:', language, error);
            }
        };

        /**
         * Initialize language state
         */
        window.MapAccessibility.prototype.initializeLanguageState = function() {
            // Set default language if not set
            if (!this.currentLanguage) {
                this.currentLanguage = 'en'; // Default to English
            }

            // Apply translations for the current language
            this.applyTranslations();

            // Remove active state from all language options
            $('.map-language-option').removeClass('active map-language-active');

            // Add active state to current language option
            $(`.map-language-option[data-language="${this.currentLanguage}"]`).addClass('active map-language-active');
        };

        /**
         * Toggle menu position section visibility
         */
        window.MapAccessibility.prototype.toggleMenuPositionSection = function() {
            const $toggle = $('#map-menu-position-toggle');
            const $content = $('#map-menu-position-content');
            const isActive = $toggle.attr('data-active') === 'true';

            if (isActive) {
                $content.slideUp(200);
                $toggle.attr('data-active', 'false');
                $toggle.removeClass('active');
                this.toggleNavigationArrow($toggle);
            } else {
                $content.slideDown(200);
                $toggle.attr('data-active', 'true');
                $toggle.addClass('active');
                this.toggleNavigationArrow($toggle);
            }
        };

        /**
         * Select a menu position
         */
        window.MapAccessibility.prototype.selectMenuPosition = function(position) {
            // Store the selected position
            this.currentMenuPosition = position;

            // Save immediately to prevent loss on quick refresh
            localStorage.setItem('map_current_menu_position', position);
            this.saveUserPreferences();

            // Update active state
            $('#map-menu-position-content .map-language-option').removeClass('active map-language-active');
            $(`#map-menu-position-content .map-language-option[data-position="${position}"]`).addClass('active map-language-active');

            // Apply the position to the widget
            this.applyMenuPosition(position);
        };

        /**
         * Apply menu position to the widget
         */
        window.MapAccessibility.prototype.applyMenuPosition = function(position) {
            const $widget = $('#map-accessibility-widget');

            // Remove all position classes
            $widget.removeClass('map-position-top-left map-position-top-right map-position-bottom-left map-position-bottom-right');

            // Add the new position class
            $widget.addClass(`map-position-${position}`);

            // Apply CSS positioning
            switch (position) {
                case 'top-left':
                    $widget.css({
                        'top': '20px',
                        'left': '20px',
                        'bottom': 'auto',
                        'right': 'auto'
                    });
                    break;
                case 'top-right':
                    $widget.css({
                        'top': '20px',
                        'right': '20px',
                        'bottom': 'auto',
                        'left': 'auto'
                    });
                    break;
                case 'bottom-left':
                    $widget.css({
                        'bottom': '20px',
                        'left': '20px',
                        'top': 'auto',
                        'right': 'auto'
                    });
                    break;
                case 'bottom-right':
                    $widget.css({
                        'bottom': '20px',
                        'right': '20px',
                        'top': 'auto',
                        'left': 'auto'
                    });
                    break;
            }
        };

        /**
         * Initialize menu position state
         */
        window.MapAccessibility.prototype.initializeMenuPositionState = function() {
            // Set default position if not set
            if (!this.currentMenuPosition) {
                this.currentMenuPosition = 'bottom-right'; // Default position
            }

            // Apply the current position
            this.applyMenuPosition(this.currentMenuPosition);

            // Set the active position option
            $('#map-menu-position-content .map-language-option').removeClass('active map-language-active');
            $(`#map-menu-position-content .map-language-option[data-position="${this.currentMenuPosition}"]`).addClass('active map-language-active');
        };



        // Preferences methods will be added here
    }

})(jQuery);
