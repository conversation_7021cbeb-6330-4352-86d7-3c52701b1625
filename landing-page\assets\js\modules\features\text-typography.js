/**
 * Text & Typography Module - Text Enhancement Features
 *
 * This module contains:
 * - ADHD Focus Mode functionality
 * - Big Cursor Mode
 * - Text Magnification Mode
 * - Text & Typography section initialization
 * - Text enhancement feature state management
 *
 * @package MyAccessibilityPlugin
 * @since 1.0.0
 */

(function($) {
    'use strict';

    // Extend MapAccessibility with text & typography functionality
    if (typeof window.MapAccessibility !== 'undefined') {

        // Add text & typography event bindings
        const originalBindEvents = window.MapAccessibility.prototype.bindEvents;
        window.MapAccessibility.prototype.bindEvents = function() {
            originalBindEvents.call(this);

            const self = this;

            // Text Magnification toggle
            $(document).on('click.mapAccessibility', '#map-text-magnification-toggle', function(e) {
                e.preventDefault();
                self.toggleTextMagnification();
            });

            // Letter Spacing toggle
            $(document).on('click.mapAccessibility', '#map-letter-spacing-toggle', function(e) {
                e.preventDefault();
                e.stopPropagation();
                self.toggleLetterSpacingSection();
            });

            // Text Alignment toggle
            $(document).on('click.mapAccessibility', '#map-text-alignment-toggle', function(e) {
                e.preventDefault();
                e.stopPropagation();
                self.toggleTextAlignmentSection();
            });

            // Font Size controls
            $(document).on('click.mapAccessibility', '#map-font-size-increase', function(e) {
                e.preventDefault();
                self.increaseFontSize();
            });

            $(document).on('click.mapAccessibility', '#map-font-size-decrease', function(e) {
                e.preventDefault();
                self.decreaseFontSize();
            });

            $(document).on('click.mapAccessibility', '#map-font-size-reset', function(e) {
                e.preventDefault();
                self.resetFontSize();
            });

            // Line Spacing controls
            $(document).on('input.mapAccessibility', '#map-line-spacing-slider', function() {
                const spacing = parseFloat($(this).val());
                self.setLineSpacing(spacing);
            });

            $(document).on('click.mapAccessibility', '#map-line-spacing-reset', function(e) {
                e.preventDefault();
                self.resetLineSpacing();
            });

            // Letter Spacing controls
            $(document).on('input.mapAccessibility', '#map-letter-spacing-slider', function() {
                const spacing = parseFloat($(this).val());
                self.setLetterSpacing(spacing);
            });

            $(document).on('click.mapAccessibility', '#map-letter-spacing-reset', function(e) {
                e.preventDefault();
                self.resetLetterSpacing();
            });

            // Text Alignment controls
            $(document).on('click.mapAccessibility', '.map-alignment-btn', function(e) {
                e.preventDefault();
                const alignment = $(this).data('alignment');
                self.setTextAlignment(alignment);
            });

            $(document).on('click.mapAccessibility', '#map-text-alignment-reset', function(e) {
                e.preventDefault();
                self.resetTextAlignment();
            });
        };



        /**
         * Restore Text Magnification Mode state from localStorage
         */
        window.MapAccessibility.prototype.restoreTextMagnificationModeState = function() {
            setTimeout(() => {
                const $toggle = $('#map-text-magnification-toggle');
                if ($toggle.length) {
                    $toggle.attr('data-active', 'true');
                    $toggle.addClass('active');

                    this.enableTextMagnification();
                }
            }, 200);
        };

        /**
         * Initialize navigation section features
         */
        window.MapAccessibility.prototype.initializeNavigationSection = function() {
            // Text Magnification toggle
            $('#map-text-magnification-toggle').off('click.navigation').on('click.navigation', (e) => {
                e.preventDefault();
                this.toggleTextMagnification();
            });
        };



        /**
         * Toggle Text Magnification Mode
         */
        window.MapAccessibility.prototype.toggleTextMagnification = function() {
            const $toggle = $('#map-text-magnification-toggle');
            const isActive = $toggle.attr('data-active') === 'true';

            if (isActive) {
                $toggle.attr('data-active', 'false').removeClass('active');
                this.disableTextMagnification();
                this.textMagnificationMode = false;
            } else {
                $toggle.attr('data-active', 'true').addClass('active');
                this.enableTextMagnification();
                this.textMagnificationMode = true;
            }

            this.saveUserPreferences();
        };

        /**
         * Enable text magnification mode
         */
        window.MapAccessibility.prototype.enableTextMagnification = function() {
            $('body').addClass('map-text-magnification-mode');

            if (!$('#map-magnification-window').length) {
                $('body').append(`
                    <div id="map-magnification-window" class="map-magnification-window" style="display: none;">
                        <div class="map-magnification-content"></div>
                    </div>
                `);
            }

            let magnificationTimeout;
            const throttleDelay = 16;

            const textElements = 'p, span, a, h1, h2, h3, h4, h5, h6, li, td, th, div, label, button, input[type="text"], input[type="email"], textarea';

            $(document).off('mouseenter.textMagnification mouseleave.textMagnification')
                .on('mouseenter.textMagnification', textElements, (e) => {
                    if (!this.textMagnificationMode) return;

                    if ($(e.target).closest('.map-widget-panel, .map-main-toggle, .map-magnification-window').length > 0) {
                        return;
                    }

                    clearTimeout(magnificationTimeout);
                    magnificationTimeout = setTimeout(() => {
                        this.showMagnificationWindow(e);
                    }, throttleDelay);
                })
                .on('mouseleave.textMagnification', textElements, () => {
                    if (!this.textMagnificationMode) return;

                    clearTimeout(magnificationTimeout);
                    this.hideMagnificationWindow();
                });

            $(window).off('scroll.textMagnification').on('scroll.textMagnification', () => {
                if (this.textMagnificationMode) {
                    this.hideMagnificationWindow();
                }
            });
        };

        /**
         * Disable text magnification mode
         */
        window.MapAccessibility.prototype.disableTextMagnification = function() {
            $('body').removeClass('map-text-magnification-mode');
            $(document).off('mouseenter.textMagnification mouseleave.textMagnification');
            $(window).off('scroll.textMagnification');
            this.hideMagnificationWindow();
            $('#map-magnification-window').remove();
        };

        /**
         * Show magnification window with text content
         */
        window.MapAccessibility.prototype.showMagnificationWindow = function(e) {
            const $target = $(e.target);
            const $window = $('#map-magnification-window');
            const $content = $window.find('.map-magnification-content');

            let textContent = $target.text().trim();

            if (!textContent || textContent.length < 3) {
                const $textParent = $target.closest('p, span, a, h1, h2, h3, h4, h5, h6, li, td, th, div, label, button').first();
                textContent = $textParent.text().trim();
            }

            if (!textContent || textContent.length < 3) {
                return;
            }

            if (textContent.length > 150) {
                textContent = textContent.substring(0, 150) + '...';
            }

            $content.text(textContent);
            this.positionMagnificationWindow(e, $window);
            $window.stop(true, true).fadeIn(200);
        };

        /**
         * Hide magnification window
         */
        window.MapAccessibility.prototype.hideMagnificationWindow = function() {
            const $window = $('#map-magnification-window');
            $window.stop(true, true).fadeOut(150);
        };

        /**
         * Position magnification window relative to cursor
         */
        window.MapAccessibility.prototype.positionMagnificationWindow = function(e, $window) {
            const mouseX = e.pageX;
            const mouseY = e.pageY;
            const windowWidth = $(window).width();
            const scrollTop = $(window).scrollTop();
            const scrollLeft = $(window).scrollLeft();

            $window.css({ visibility: 'hidden', display: 'block' });
            const magWidth = $window.outerWidth();
            const magHeight = $window.outerHeight();
            $window.css({ visibility: 'visible', display: 'none' });

            let left = mouseX + 15;
            let top = mouseY - magHeight - 15;

            if (left + magWidth > scrollLeft + windowWidth) {
                left = mouseX - magWidth - 15;
            }

            if (top < scrollTop) {
                top = mouseY + 15;
            }

            left = Math.max(scrollLeft + 10, left);
            top = Math.max(scrollTop + 10, top);

            $window.css({
                left: left + 'px',
                top: top + 'px'
            });
        };



        /**
         * Increase font size
         */
        window.MapAccessibility.prototype.increaseFontSize = function() {
            if (this.currentFontSize < 150) {
                this.currentFontSize += 5;
                this.applyFontSize();
                this.updateFontSizeDisplay();
                this.showFontSizeStatus();
                this.saveUserPreferences();

                const $controls = $('#map-font-size-controls');
                if ($controls.length && !$controls.is(':visible')) {
                    $controls.slideDown(200);
                    const $toggle = $('#map-font-size-toggle');
                    if ($toggle.length) {
                        $toggle.attr('data-active', 'true');
                        $toggle.addClass('active');
                    }
                }
            }

            this.updateFontSizeButtons();
        };

        /**
         * Decrease font size
         */
        window.MapAccessibility.prototype.decreaseFontSize = function() {
            if (this.currentFontSize > 75) {
                this.currentFontSize -= 5;
                this.applyFontSize();
                this.updateFontSizeDisplay();
                this.showFontSizeStatus();
                this.saveUserPreferences();

                const $controls = $('#map-font-size-controls');
                if ($controls.length && !$controls.is(':visible')) {
                    $controls.slideDown(200);
                    const $toggle = $('#map-font-size-toggle');
                    if ($toggle.length) {
                        $toggle.attr('data-active', 'true');
                        $toggle.addClass('active');
                    }
                }
            }

            this.updateFontSizeButtons();
        };

        /**
         * Reset font size to default (100%)
         */
        window.MapAccessibility.prototype.resetFontSize = function() {
            this.currentFontSize = 100;
            this.applyFontSize();
            this.updateFontSizeDisplay();
            this.showFontSizeStatus();
            this.saveUserPreferences();
            this.updateFontSizeButtons();
        };

        /**
         * Apply font size to the page
         */
        window.MapAccessibility.prototype.applyFontSize = function() {
            $('#map-font-size-style').remove();

            if (this.currentFontSize !== 100) {
                const fontSizeRatio = this.currentFontSize / 100;
                const cssRules = `
                    html.map-font-size-active body,
                    html.map-font-size-active p:not(.map-accessibility-widget *),
                    html.map-font-size-active span:not(.map-accessibility-widget *),
                    html.map-font-size-active div:not(.map-accessibility-widget):not(.map-accessibility-widget *),
                    html.map-font-size-active li:not(.map-accessibility-widget *),
                    html.map-font-size-active a:not(.map-accessibility-widget *),
                    html.map-font-size-active td:not(.map-accessibility-widget *),
                    html.map-font-size-active th:not(.map-accessibility-widget *),
                    html.map-font-size-active label:not(.map-accessibility-widget *),
                    html.map-font-size-active input:not(.map-accessibility-widget *),
                    html.map-font-size-active textarea:not(.map-accessibility-widget *),
                    html.map-font-size-active select:not(.map-accessibility-widget *),
                    html.map-font-size-active button:not(.map-accessibility-widget *),
                    html.map-font-size-active h1:not(.map-accessibility-widget *),
                    html.map-font-size-active h2:not(.map-accessibility-widget *),
                    html.map-font-size-active h3:not(.map-accessibility-widget *),
                    html.map-font-size-active h4:not(.map-accessibility-widget *),
                    html.map-font-size-active h5:not(.map-accessibility-widget *),
                    html.map-font-size-active h6:not(.map-accessibility-widget *) {
                        font-size: ${fontSizeRatio}em !important;
                    }
                `;

                $('<style id="map-font-size-style">' + cssRules + '</style>').appendTo('head');
                $('html').addClass('map-font-size-active');
            } else {
                $('html').removeClass('map-font-size-active');
            }
        };

        /**
         * Update font size display
         */
        window.MapAccessibility.prototype.updateFontSizeDisplay = function() {
            let displayText;

            if (this.currentFontSize === 100) {
                displayText = 'Default';
            } else if (this.currentFontSize > 100) {
                const increase = this.currentFontSize - 100;
                displayText = `+${increase}%`;
            } else {
                const decrease = 100 - this.currentFontSize;
                displayText = `−${decrease}%`;
            }

            $('#map-font-size-value').text(displayText);
            $('#map-font-percentage').text(`${this.currentFontSize}%`);

            const $preview = $('.map-size-preview');
            if ($preview.length) {
                const scaleFactor = this.currentFontSize / 100;
                $preview.css('font-size', `${24 * scaleFactor}px`);
            }
        };

        /**
         * Update font size button states
         */
        window.MapAccessibility.prototype.updateFontSizeButtons = function() {
            const $increaseBtn = $('#map-font-size-increase');
            const $decreaseBtn = $('#map-font-size-decrease');

            if (this.currentFontSize >= 150) {
                $increaseBtn.prop('disabled', true);
            } else {
                $increaseBtn.prop('disabled', false);
            }

            if (this.currentFontSize <= 75) {
                $decreaseBtn.prop('disabled', true);
            } else {
                $decreaseBtn.prop('disabled', false);
            }
        };

        /**
         * Show font size status message - Disabled for cleaner interface
         */
        window.MapAccessibility.prototype.showFontSizeStatus = function() {
            return;
        };

        /**
         * Restore font size state from localStorage
         */
        window.MapAccessibility.prototype.restoreFontSizeState = function() {
            this.applyFontSize();
            this.updateFontSizeDisplay();
            this.updateFontSizeButtons();
        };

        /**
         * Set line spacing
         * @param {number} spacing - Line spacing value (1.0 to 2.5)
         */
        window.MapAccessibility.prototype.setLineSpacing = function(spacing) {
            spacing = Math.max(1.0, Math.min(2.5, spacing));

            this.currentLineSpacing = spacing;
            this.applyLineSpacing();
            this.updateLineSpacingDisplay();
            this.saveUserPreferences();

            if (spacing !== 1.5) {
                const $controls = $('#map-line-spacing-controls');
                if ($controls.length && !$controls.is(':visible')) {
                    $controls.slideDown(200);
                    const $toggle = $('#map-line-spacing-toggle');
                    if ($toggle.length) {
                        $toggle.attr('data-active', 'true');
                        $toggle.addClass('active');
                    }
                }
            }
        };

        /**
         * Reset line spacing to default (1.5)
         */
        window.MapAccessibility.prototype.resetLineSpacing = function() {
            this.currentLineSpacing = 1.5;
            this.applyLineSpacing();
            this.updateLineSpacingDisplay();
            this.updateLineSpacingSlider();
            this.saveUserPreferences();
        };

        /**
         * Apply line spacing to the page
         */
        window.MapAccessibility.prototype.applyLineSpacing = function() {
            $('#map-line-spacing-style').remove();

            if (this.currentLineSpacing !== 1.5) {
                const cssRules = `
                    html.map-line-spacing-active body,
                    html.map-line-spacing-active p,
                    html.map-line-spacing-active span,
                    html.map-line-spacing-active div,
                    html.map-line-spacing-active li,
                    html.map-line-spacing-active a,
                    html.map-line-spacing-active td,
                    html.map-line-spacing-active th,
                    html.map-line-spacing-active label,
                    html.map-line-spacing-active h1,
                    html.map-line-spacing-active h2,
                    html.map-line-spacing-active h3,
                    html.map-line-spacing-active h4,
                    html.map-line-spacing-active h5,
                    html.map-line-spacing-active h6,
                    html.map-line-spacing-active blockquote,
                    html.map-line-spacing-active pre {
                        line-height: ${this.currentLineSpacing} !important;
                    }

                    /* Preserve accessibility widget line spacing */
                    html.map-line-spacing-active .map-accessibility-widget,
                    html.map-line-spacing-active .map-accessibility-widget * {
                        line-height: 1.4 !important;
                    }
                `;

                $('<style id="map-line-spacing-style">' + cssRules + '</style>').appendTo('head');
                $('html').addClass('map-line-spacing-active');
            } else {
                $('html').removeClass('map-line-spacing-active');
            }
        };

        /**
         * Update line spacing display
         */
        window.MapAccessibility.prototype.updateLineSpacingDisplay = function() {
            let displayText;

            if (this.currentLineSpacing === 1.5) {
                displayText = 'Default';
            } else if (this.currentLineSpacing < 1.5) {
                displayText = 'Tight';
            } else {
                displayText = 'Wide';
            }

            $('#map-line-spacing-value').text(displayText);
            $('#map-spacing-numeric').text(`${this.currentLineSpacing}x`);

            const $previewLines = $('#map-spacing-preview');
            if ($previewLines.length) {
                $previewLines.css('line-height', this.currentLineSpacing);
            }

            const progress = ((this.currentLineSpacing - 1.0) / (2.5 - 1.0)) * 100;
            $('#map-slider-progress').css('width', `${progress}%`);
        };

        /**
         * Update line spacing slider position
         */
        window.MapAccessibility.prototype.updateLineSpacingSlider = function() {
            $('#map-line-spacing-slider').val(this.currentLineSpacing);
        };

        /**
         * Restore line spacing state from localStorage
         */
        window.MapAccessibility.prototype.restoreLineSpacingState = function() {
            this.applyLineSpacing();
            this.updateLineSpacingDisplay();
            this.updateLineSpacingSlider();
        };

        /**
         * Toggle Letter Spacing section
         */
        window.MapAccessibility.prototype.toggleLetterSpacingSection = function() {
            const $toggle = $('#map-letter-spacing-toggle');
            const $controls = $('#map-letter-spacing-controls');
            const isActive = $toggle.attr('data-active') === 'true';

            if (isActive) {
                $controls.slideUp(200);
                $toggle.attr('data-active', 'false');
                $toggle.removeClass('active');
                this.toggleNavigationArrow($toggle);
            } else {
                $controls.slideDown(200);
                $toggle.attr('data-active', 'true');
                $toggle.addClass('active');
                this.toggleNavigationArrow($toggle);
            }
        };

        /**
         * Toggle Text Alignment section
         */
        window.MapAccessibility.prototype.toggleTextAlignmentSection = function() {
            const $toggle = $('#map-text-alignment-toggle');
            const $controls = $('#map-text-alignment-controls');
            const isActive = $toggle.attr('data-active') === 'true';

            if (isActive) {
                $controls.slideUp(200);
                $toggle.attr('data-active', 'false');
                $toggle.removeClass('active');
                this.toggleNavigationArrow($toggle);
            } else {
                $controls.slideDown(200);
                $toggle.attr('data-active', 'true');
                $toggle.addClass('active');
                this.toggleNavigationArrow($toggle);
            }
        };

        /**
         * Set letter spacing
         * @param {number} spacing - Letter spacing value (0 to 0.2)
         */
        window.MapAccessibility.prototype.setLetterSpacing = function(spacing) {
            spacing = Math.max(0, Math.min(0.2, spacing));

            this.currentLetterSpacing = spacing;
            this.applyLetterSpacing();
            this.updateLetterSpacingDisplay();
            this.saveUserPreferences();

            if (spacing !== 0) {
                const $controls = $('#map-letter-spacing-controls');
                if ($controls.length && !$controls.is(':visible')) {
                    $controls.slideDown(200);
                    const $toggle = $('#map-letter-spacing-toggle');
                    if ($toggle.length) {
                        $toggle.attr('data-active', 'true');
                        $toggle.addClass('active');
                    }
                }
            }
        };

        /**
         * Reset letter spacing to default (0)
         */
        window.MapAccessibility.prototype.resetLetterSpacing = function() {
            this.currentLetterSpacing = 0;
            this.applyLetterSpacing();
            this.updateLetterSpacingDisplay();
            this.updateLetterSpacingSlider();
            this.saveUserPreferences();
        };

        /**
         * Apply letter spacing to the page
         */
        window.MapAccessibility.prototype.applyLetterSpacing = function() {
            $('#map-letter-spacing-style').remove();

            if (this.currentLetterSpacing !== 0) {
                const cssRules = `
                    html.map-letter-spacing-active body,
                    html.map-letter-spacing-active p,
                    html.map-letter-spacing-active span,
                    html.map-letter-spacing-active div,
                    html.map-letter-spacing-active li,
                    html.map-letter-spacing-active a,
                    html.map-letter-spacing-active td,
                    html.map-letter-spacing-active th,
                    html.map-letter-spacing-active label,
                    html.map-letter-spacing-active h1,
                    html.map-letter-spacing-active h2,
                    html.map-letter-spacing-active h3,
                    html.map-letter-spacing-active h4,
                    html.map-letter-spacing-active h5,
                    html.map-letter-spacing-active h6,
                    html.map-letter-spacing-active blockquote,
                    html.map-letter-spacing-active pre {
                        letter-spacing: ${this.currentLetterSpacing}em !important;
                    }

                    /* Preserve accessibility widget letter spacing */
                    html.map-letter-spacing-active .map-accessibility-widget,
                    html.map-letter-spacing-active .map-accessibility-widget * {
                        letter-spacing: normal !important;
                    }
                `;

                $('<style id="map-letter-spacing-style">' + cssRules + '</style>').appendTo('head');
                $('html').addClass('map-letter-spacing-active');
            } else {
                $('html').removeClass('map-letter-spacing-active');
            }
        };

        /**
         * Update letter spacing display
         */
        window.MapAccessibility.prototype.updateLetterSpacingDisplay = function() {
            let displayText;

            if (this.currentLetterSpacing === 0) {
                displayText = 'Default';
            } else if (this.currentLetterSpacing < 0.05) {
                displayText = 'Tight';
            } else {
                displayText = 'Wide';
            }

            $('#map-letter-spacing-value').text(displayText);
            $('#map-letter-spacing-numeric').text(`${this.currentLetterSpacing}em`);

            const progress = (this.currentLetterSpacing / 0.2) * 100;
            $('#map-letter-slider-progress').css('width', `${progress}%`);
        };

        /**
         * Update letter spacing slider position
         */
        window.MapAccessibility.prototype.updateLetterSpacingSlider = function() {
            $('#map-letter-spacing-slider').val(this.currentLetterSpacing);
        };

        /**
         * Restore letter spacing state from localStorage
         */
        window.MapAccessibility.prototype.restoreLetterSpacingState = function() {
            this.applyLetterSpacing();
            this.updateLetterSpacingDisplay();
            this.updateLetterSpacingSlider();
        };

        /**
         * Set text alignment
         * @param {string} alignment - Text alignment value (left, center, right, justify)
         */
        window.MapAccessibility.prototype.setTextAlignment = function(alignment) {
            const validAlignments = ['left', 'center', 'right', 'justify'];
            if (!validAlignments.includes(alignment)) {
                alignment = 'left';
            }

            this.currentTextAlignment = alignment;
            this.applyTextAlignment();
            this.updateTextAlignmentDisplay();
            this.updateTextAlignmentButtons();
            this.saveUserPreferences();

            if (alignment !== 'left') {
                const $controls = $('#map-text-alignment-controls');
                if ($controls.length && !$controls.is(':visible')) {
                    $controls.slideDown(200);
                    const $toggle = $('#map-text-alignment-toggle');
                    if ($toggle.length) {
                        $toggle.attr('data-active', 'true');
                        $toggle.addClass('active');
                    }
                }
            }
        };

        /**
         * Reset text alignment to default (left)
         */
        window.MapAccessibility.prototype.resetTextAlignment = function() {
            this.currentTextAlignment = 'left';
            this.applyTextAlignment();
            this.updateTextAlignmentDisplay();
            this.updateTextAlignmentButtons();
            this.saveUserPreferences();
        };

        /**
         * Apply text alignment to the page
         */
        window.MapAccessibility.prototype.applyTextAlignment = function() {
            $('#map-text-alignment-style').remove();

            if (this.currentTextAlignment !== 'left') {
                const cssRules = `
                    html.map-text-alignment-active body,
                    html.map-text-alignment-active p,
                    html.map-text-alignment-active div,
                    html.map-text-alignment-active li,
                    html.map-text-alignment-active td,
                    html.map-text-alignment-active th,
                    html.map-text-alignment-active h1,
                    html.map-text-alignment-active h2,
                    html.map-text-alignment-active h3,
                    html.map-text-alignment-active h4,
                    html.map-text-alignment-active h5,
                    html.map-text-alignment-active h6,
                    html.map-text-alignment-active blockquote {
                        text-align: ${this.currentTextAlignment} !important;
                    }

                    /* Preserve accessibility widget alignment */
                    html.map-text-alignment-active .map-accessibility-widget,
                    html.map-text-alignment-active .map-accessibility-widget * {
                        text-align: left !important;
                    }
                `;

                $('<style id="map-text-alignment-style">' + cssRules + '</style>').appendTo('head');
                $('html').addClass('map-text-alignment-active');
            } else {
                $('html').removeClass('map-text-alignment-active');
            }
        };

        /**
         * Update text alignment display
         */
        window.MapAccessibility.prototype.updateTextAlignmentDisplay = function() {
            let displayText;

            switch (this.currentTextAlignment) {
                case 'left':
                    displayText = 'Default';
                    break;
                case 'center':
                    displayText = 'Center';
                    break;
                case 'right':
                    displayText = 'Right';
                    break;
                case 'justify':
                    displayText = 'Justify';
                    break;
                default:
                    displayText = 'Default';
            }

            $('#map-text-alignment-value').text(displayText);
        };

        /**
         * Update text alignment button states
         */
        window.MapAccessibility.prototype.updateTextAlignmentButtons = function() {
            $('.map-alignment-btn').removeClass('active');
            $(`#map-align-${this.currentTextAlignment}`).addClass('active');
        };

        /**
         * Restore text alignment state from localStorage
         */
        window.MapAccessibility.prototype.restoreTextAlignmentState = function() {
            this.applyTextAlignment();
            this.updateTextAlignmentDisplay();
            this.updateTextAlignmentButtons();
        };

        // Text & Typography methods completed
    }

})(jQuery);
