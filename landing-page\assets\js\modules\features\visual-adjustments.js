/**
 * Themes Module - Visual Themes and Color Management
 *
 * This module contains:
 * - Contrast themes (normal, dark, high-contrast, etc.)
 * - Custom theme colors and color studio
 * - Theme navigation and selection
 * - Theme state management and restoration
 *
 * @package MyAccessibilityPlugin
 * @since 1.0.0
 */

(function($) {
    'use strict';

    // Theme selector properties and data
    const themes = [
        {
            id: 'normal',
            name: 'Default',
            description: 'Standard website appearance with default colors',
            icon: '<svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor"><path d="M10 20v-6h4v6h5v-8h3L12 3 2 12h3v8z"/></svg>'
        },
        {
            id: 'monochrome',
            name: 'Monochrome',
            description: 'Grayscale colors for reduced visual distraction',
            icon: '<svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor"><rect x="3" y="6" width="3" height="12" fill="currentColor"/><rect x="7" y="6" width="3" height="12" fill="currentColor" opacity="0.7"/><rect x="11" y="6" width="3" height="12" fill="currentColor" opacity="0.4"/><rect x="15" y="6" width="3" height="12" fill="currentColor" opacity="0.2"/><rect x="19" y="6" width="2" height="12" fill="currentColor" opacity="0.1"/></svg>'
        },
        {
            id: 'low-saturation',
            name: 'Low Saturation',
            description: 'Reduced color intensity for comfortable viewing',
            icon: '<svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor"><circle cx="12" cy="12" r="8" fill="none" stroke="currentColor" stroke-width="2" opacity="0.4"/><circle cx="12" cy="8" r="2" fill="currentColor" opacity="0.3"/><circle cx="8" cy="14" r="1.5" fill="currentColor" opacity="0.3"/><circle cx="16" cy="14" r="1.5" fill="currentColor" opacity="0.3"/></svg>'
        },
        {
            id: 'high-saturation',
            name: 'High Saturation',
            description: 'Enhanced color intensity for vibrant viewing',
            icon: '<svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor"><path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/></svg>'
        },
        {
            id: 'dark',
            name: 'Dark Mode',
            description: 'Dark background with light text for low-light environments',
            icon: '<svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor"><path d="M21 12.79A9 9 0 1 1 11.21 3 7 7 0 0 0 21 12.79z"/></svg>'
        },
        {
            id: 'high-contrast',
            name: 'High Contrast',
            description: 'Black and white for maximum readability',
            icon: '<svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor"><path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 18V4c4.41 0 8 3.59 8 8s-3.59 8-8 8z"/></svg>'
        },
        {
            id: 'sepia',
            name: 'Sepia',
            description: 'Warm, paper-like colors for comfortable reading',
            icon: '<svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor"><rect x="3" y="4" width="18" height="14" rx="2" fill="none" stroke="currentColor" stroke-width="1.5"/><circle cx="8" cy="9" r="1.5" fill="currentColor"/><path d="M21 15l-3.5-3.5c-.4-.4-1-.4-1.4 0L6 21" stroke="currentColor" stroke-width="1.5" fill="none"/><rect x="2" y="2" width="20" height="20" fill="none" stroke="currentColor" stroke-width="0.5" opacity="0.3" rx="1"/></svg>'
        },
        {
            id: 'colorblind',
            name: 'Color Blind Friendly',
            description: 'Optimized colors for color vision deficiency',
            icon: '<svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor"><path d="M12 4.5C7 4.5 2.73 7.61 1 12c1.73 4.39 6 7.5 11 7.5s9.27-3.11 11-7.5c-1.73-4.39-6-7.5-11-7.5z"/><circle cx="12" cy="12" r="3" fill="white"/><path d="M12 9v6M9 12h6" stroke="currentColor" stroke-width="1.5"/></svg>'
        }
    ];

    // Extend MapAccessibility with theme event handlers
    if (typeof window.MapAccessibility !== 'undefined') {

        // Add theme properties to the main class
        const originalConstructor = window.MapAccessibility.prototype.constructor;
        window.MapAccessibility.prototype.constructor = function() {
            originalConstructor.call(this);
            this.themes = themes;
            this.currentThemeIndex = 0;
            this.selectedTheme = 'normal';
        };

        // Add theme event bindings
        const originalBindEvents = window.MapAccessibility.prototype.bindEvents;
        window.MapAccessibility.prototype.bindEvents = function() {
            originalBindEvents.call(this);

            const self = this;

            // Dark Mode toggle
            $(document).on('click.mapAccessibility', '#map-dark-mode-toggle', function(e) {
                e.preventDefault();
                self.toggleDarkMode();
            });

            // Contrast Themes toggle functionality
            $(document).on('click.mapAccessibility', '#map-contrast-themes-toggle', function(e) {
                e.preventDefault();
                e.stopPropagation();
                self.toggleContrastThemesSection();
            });

            // Custom Theme Colors functionality
            $(document).on('click.mapAccessibility', '#map-custom-theme-toggle', function(e) {
                e.preventDefault();
                e.stopPropagation();
                self.toggleCustomThemeSection();
            });

            $(document).on('input.mapAccessibility change.mapAccessibility click.mapAccessibility', '.map-color-picker, .map-color-picker-compact', function() {
                self.applyIndividualColorChange($(this));
                self.updateColorPreview($(this));
                self.toggleResetButtonVisibility($(this));
            });

            // Individual color reset buttons
            $(document).on('click.mapAccessibility', '.map-color-reset, .map-color-reset-btn', function(e) {
                e.preventDefault();
                e.stopPropagation();
                const targetId = $(this).data('target');
                self.resetIndividualColor(targetId);
            });

            // Make entire color row clickable to open color picker
            $(document).on('click.mapAccessibility', '.map-color-row', function(e) {
                if ($(e.target).is('.map-color-picker-compact, .map-color-reset-btn, .map-color-reset-btn svg, .map-color-reset-btn path')) {
                    return;
                }

                e.preventDefault();
                const $colorInput = $(this).find('.map-color-picker-compact');
                if ($colorInput.length) {
                    $colorInput[0].click();
                }
            });

            // Theme Selector Navigation
            $(document).on('click.mapAccessibility', '#map-theme-prev', function() {
                self.navigateTheme('prev');
            });

            $(document).on('click.mapAccessibility', '#map-theme-next', function() {
                self.navigateTheme('next');
            });

            // Theme dots navigation
            $(document).on('click.mapAccessibility', '.map-theme-dot', function() {
                const theme = $(this).data('theme');
                if (theme) {
                    self.selectTheme(theme);
                }
            });

            // Keyboard navigation for theme selector
            $(document).on('keydown.mapAccessibility', '.map-theme-selector', function(e) {
                if (e.key === 'ArrowLeft') {
                    e.preventDefault();
                    self.navigateTheme('prev');
                } else if (e.key === 'ArrowRight') {
                    e.preventDefault();
                    self.navigateTheme('next');
                } else if (e.key === 'Enter' || e.key === ' ') {
                    e.preventDefault();
                    self.showThemeAppliedFeedback();
                }
            });
        };

        /**
         * Apply contrast theme
         * @param {string} theme - Theme name (normal, dark, high-contrast, sepia, colorblind)
         */
        window.MapAccessibility.prototype.applyContrastTheme = function(theme) {
            const validThemes = ['normal', 'monochrome', 'low-saturation', 'high-saturation', 'dark', 'high-contrast', 'sepia', 'colorblind', 'custom'];
            if (!validThemes.includes(theme)) {
                return;
            }

            // Remove all existing theme classes and custom styles
            $('body').removeClass('map-theme-normal map-theme-monochrome map-theme-low-saturation map-theme-high-saturation map-theme-dark map-theme-high-contrast map-theme-sepia map-theme-colorblind map-theme-custom');
            $('#map-custom-theme-styles').remove();

            // Apply new theme class
            if (theme !== 'normal') {
                if (theme === 'custom') {
                    this.applyCustomTheme();
                    return;
                } else {
                    $('body').addClass(`map-theme-${theme}`);
                }
            }

            this.currentContrastTheme = theme;
            this.updateContrastThemeUI(theme);
            this.saveUserPreferences();
        };

        /**
         * Update contrast theme UI
         * @param {string} theme - Current theme name
         */
        window.MapAccessibility.prototype.updateContrastThemeUI = function(theme) {
            $('.map-theme-button').removeClass('map-theme-active');
            $(`#map-theme-${theme}`).addClass('map-theme-active');
        };

        /**
         * Restore contrast theme state from localStorage
         */
        window.MapAccessibility.prototype.restoreContrastThemeState = function() {
            this.applyContrastTheme(this.currentContrastTheme);

            if (this.currentContrastTheme === 'custom' && this.isCustomThemeActive) {
                setTimeout(() => {
                    this.initializeCustomThemeColors();
                }, 100);
            }

            if ($('#map-theme-preview').length) {
                this.initializeThemeSelector();
            }
        };

        /**
         * Toggle contrast themes section visibility
         */
        window.MapAccessibility.prototype.toggleContrastThemesSection = function() {
            const $toggle = $('#map-contrast-themes-toggle');
            const $content = $('#map-contrast-themes-content');
            const isActive = $toggle.attr('data-active') === 'true';

            if (isActive) {
                $content.slideUp(200);
                $toggle.attr('data-active', 'false');
                $toggle.removeClass('active');
                this.toggleNavigationArrow($toggle);
            } else {
                $content.slideDown(200);
                $toggle.attr('data-active', 'true');
                $toggle.addClass('active');
                this.toggleNavigationArrow($toggle);
            }
        };

        /**
         * Toggle custom theme section visibility
         */
        window.MapAccessibility.prototype.toggleCustomThemeSection = function() {
            const $toggle = $('#map-custom-theme-toggle');
            const $content = $('#map-custom-theme-content');
            const isActive = $toggle.attr('data-active') === 'true';

            if (isActive) {
                $content.slideUp(200);
                $toggle.attr('data-active', 'false');
                $toggle.removeClass('active');
                this.toggleNavigationArrow($toggle);
            } else {
                $content.slideDown(200);
                $toggle.attr('data-active', 'true');
                $toggle.addClass('active');
                this.toggleNavigationArrow($toggle);
                this.initializeCustomThemeColors();

                setTimeout(() => {
                    this.ensureCustomThemeElementsClickable();
                }, 350);
            }
        };

        /**
         * Ensure custom theme elements are clickable
         */
        window.MapAccessibility.prototype.ensureCustomThemeElementsClickable = function() {
            const $content = $('#map-custom-theme-content');
            if ($content.is(':visible')) {
                $content.find('.map-color-picker, .map-color-picker-compact, .map-custom-theme-btn').each(function() {
                    $(this).css({
                        'pointer-events': 'auto',
                        'position': 'relative',
                        'z-index': '10'
                    });
                });
            }
        };

        /**
         * Initialize custom theme color pickers with current values
         */
        window.MapAccessibility.prototype.initializeCustomThemeColors = function() {
            $('#map-custom-text-color').val(this.customThemeColors.text);
            $('#map-custom-bg-color').val(this.customThemeColors.background);
            $('#map-custom-link-color').val(this.customThemeColors.link);
            $('#map-custom-heading-color').val(this.customThemeColors.heading);
        };

        /**
         * Apply individual color change without affecting other colors
         */
        window.MapAccessibility.prototype.applyIndividualColorChange = function($colorInput) {
            const inputId = $colorInput.attr('id');
            const colorValue = $colorInput.val();

            if (!colorValue) {
                return;
            }

            switch (inputId) {
                case 'map-custom-text-color':
                    this.customThemeColors.text = colorValue;
                    break;
                case 'map-custom-bg-color':
                    this.customThemeColors.background = colorValue;
                    break;
                case 'map-custom-link-color':
                    this.customThemeColors.link = colorValue;
                    break;
                case 'map-custom-heading-color':
                    this.customThemeColors.heading = colorValue;
                    break;
                default:
                    return;
            }

            this.applySpecificColorToCSS(inputId, colorValue);
            this.isCustomThemeActive = true;
            this.currentContrastTheme = 'custom';
            this.saveUserPreferences();
        };

        /**
         * Apply specific color to CSS without affecting other colors
         */
        window.MapAccessibility.prototype.applySpecificColorToCSS = function(inputId, colorValue) {
            $('body').addClass('map-theme-custom');

            let $styleElement = $('#map-custom-theme-styles');
            if ($styleElement.length === 0) {
                $styleElement = $('<style id="map-custom-theme-styles"></style>');
                $('head').append($styleElement);
            }

            let existingCSS = $styleElement.html();

            switch (inputId) {
                case 'map-custom-text-color':
                    existingCSS = this.updateCSSColorProperty(existingCSS, 'text', colorValue);
                    break;
                case 'map-custom-bg-color':
                    existingCSS = this.updateCSSColorProperty(existingCSS, 'background', colorValue);
                    break;
                case 'map-custom-link-color':
                    existingCSS = this.updateCSSColorProperty(existingCSS, 'link', colorValue);
                    break;
                case 'map-custom-heading-color':
                    existingCSS = this.updateCSSColorProperty(existingCSS, 'heading', colorValue);
                    break;
            }

            $styleElement.html(existingCSS);
        };

        /**
         * Update specific color property in CSS string
         */
        window.MapAccessibility.prototype.updateCSSColorProperty = function(cssString, colorType, colorValue) {
            if (!cssString) {
                cssString = `
                    body.map-theme-custom {
                        /* Base styles will be added as needed */
                    }
                    body.map-theme-custom p,
                    body.map-theme-custom div:not(.map-accessibility-widget):not(.map-accessibility-widget *),
                    body.map-theme-custom span:not(.map-accessibility-widget *):not(.map-color-title):not(.map-color-desc),
                    body.map-theme-custom li,
                    body.map-theme-custom td,
                    body.map-theme-custom th {
                        /* Text color will be added as needed */
                    }
                    body.map-theme-custom h1,
                    body.map-theme-custom h2,
                    body.map-theme-custom h3,
                    body.map-theme-custom h4,
                    body.map-theme-custom h5,
                    body.map-theme-custom h6 {
                        /* Heading color will be added as needed */
                    }
                    body.map-theme-custom a {
                        /* Link color will be added as needed */
                    }
                    body.map-theme-custom main,
                    body.map-theme-custom article,
                    body.map-theme-custom section,
                    body.map-theme-custom .content,
                    body.map-theme-custom .post,
                    body.map-theme-custom .page {
                        /* Background color will be added as needed */
                    }
                `;
            }

            switch (colorType) {
                case 'text':
                    cssString = cssString.replace(
                        /(body\.map-theme-custom\s*{[^}]*?)(\s*color:\s*[^;]+;\s*)?([^}]*})/,
                        `$1 color: ${colorValue} !important; $3`
                    );
                    cssString = cssString.replace(
                        /(body\.map-theme-custom\s+p,[\s\S]*?th\s*{[^}]*?)(\s*color:\s*[^;]+;\s*)?([^}]*})/,
                        `$1 color: ${colorValue} !important; $3`
                    );
                    break;
                case 'background':
                    cssString = cssString.replace(
                        /(body\.map-theme-custom\s*{[^}]*?)(\s*background-color:\s*[^;]+;\s*)?([^}]*})/,
                        `$1 background-color: ${colorValue} !important; $3`
                    );
                    cssString = cssString.replace(
                        /(body\.map-theme-custom\s+main,[\s\S]*?\.page\s*{[^}]*?)(\s*background-color:\s*[^;]+;\s*)?([^}]*})/,
                        `$1 background-color: ${colorValue} !important; $3`
                    );
                    break;
                case 'heading':
                    cssString = cssString.replace(
                        /(body\.map-theme-custom\s+h[1-6],[\s\S]*?h6\s*{[^}]*?)(\s*color:\s*[^;]+;\s*)?([^}]*})/,
                        `$1 color: ${colorValue} !important; $3`
                    );
                    break;
                case 'link':
                    cssString = cssString.replace(
                        /(body\.map-theme-custom\s+a\s*{[^}]*?)(\s*color:\s*[^;]+;\s*)?([^}]*})/,
                        `$1 color: ${colorValue} !important; $3`
                    );
                    break;
            }

            return cssString;
        };

        /**
         * Reset individual color input
         */
        window.MapAccessibility.prototype.resetIndividualColor = function(inputId) {
            const $input = $(`#${inputId}`);
            if ($input.length) {
                $input.val('');

                switch (inputId) {
                    case 'map-custom-text-color':
                        this.customThemeColors.text = '';
                        break;
                    case 'map-custom-bg-color':
                        this.customThemeColors.background = '';
                        break;
                    case 'map-custom-link-color':
                        this.customThemeColors.link = '';
                        break;
                    case 'map-custom-heading-color':
                        this.customThemeColors.heading = '';
                        break;
                }

                this.removeColorFromCSS(inputId);
                this.updateColorPreview($input);
                this.toggleResetButtonVisibility($input);
                this.saveUserPreferences();
            }
        };

        /**
         * Remove specific color from CSS
         */
        window.MapAccessibility.prototype.removeColorFromCSS = function(inputId) {
            const $styleElement = $('#map-custom-theme-styles');
            if ($styleElement.length) {
                let cssString = $styleElement.html();

                switch (inputId) {
                    case 'map-custom-text-color':
                        cssString = cssString.replace(/color:\s*[^;]+;\s*/g, '');
                        break;
                    case 'map-custom-bg-color':
                        cssString = cssString.replace(/background-color:\s*[^;]+;\s*/g, '');
                        break;
                    case 'map-custom-link-color':
                        cssString = cssString.replace(/(body\.map-theme-custom\s+a\s*{[^}]*?)color:\s*[^;]+;\s*([^}]*})/g, '$1$2');
                        break;
                    case 'map-custom-heading-color':
                        cssString = cssString.replace(/(body\.map-theme-custom\s+h[1-6],[\s\S]*?h6\s*{[^}]*?)color:\s*[^;]+;\s*([^}]*})/g, '$1$2');
                        break;
                }

                $styleElement.html(cssString);
            }
        };

        /**
         * Update color preview display
         */
        window.MapAccessibility.prototype.updateColorPreview = function($colorInput) {
            const inputId = $colorInput.attr('id');
            const colorValue = $colorInput.val();
            const $preview = $colorInput.siblings('.map-color-preview');
            const $previewText = $preview.find('.map-color-preview-text');

            if (colorValue) {
                switch (inputId) {
                    case 'map-custom-text-color':
                        $previewText.css('color', colorValue);
                        $preview.css('background', 'rgba(255, 255, 255, 0.9)');
                        break;
                    case 'map-custom-bg-color':
                        $preview.css('background', colorValue);
                        const brightness = this.getColorBrightness(colorValue);
                        $previewText.css('color', brightness > 128 ? '#374151' : '#ffffff');
                        break;
                    case 'map-custom-link-color':
                        $previewText.css('color', colorValue);
                        $preview.css('background', 'rgba(255, 255, 255, 0.9)');
                        break;
                    case 'map-custom-heading-color':
                        $previewText.css('color', colorValue);
                        $preview.css('background', 'rgba(255, 255, 255, 0.9)');
                        break;
                }
            } else {
                $preview.css('background', 'rgba(255, 255, 255, 0.9)');
                $previewText.css('color', '#374151');
            }
        };

        /**
         * Get color brightness for contrast calculation
         */
        window.MapAccessibility.prototype.getColorBrightness = function(hexColor) {
            hexColor = hexColor.replace('#', '');

            const r = parseInt(hexColor.substr(0, 2), 16);
            const g = parseInt(hexColor.substr(2, 2), 16);
            const b = parseInt(hexColor.substr(4, 2), 16);

            return (r * 299 + g * 587 + b * 114) / 1000;
        };

        /**
         * Show color reset feedback animation
         */
        window.MapAccessibility.prototype.showColorResetFeedback = function($input) {
            const $card = $input.closest('.map-color-card');
            $card.addClass('map-reset-feedback');

            setTimeout(() => {
                $card.removeClass('map-reset-feedback');
            }, 600);
        };

        /**
         * Apply custom theme automatically when colors change (with debouncing)
         */
        window.MapAccessibility.prototype.applyCustomThemeAutomatically = function() {
            try {
                if (this.customThemeDebounceTimer) {
                    clearTimeout(this.customThemeDebounceTimer);
                }

                const textColor = $('#map-custom-text-color').val() || '';
                const bgColor = $('#map-custom-bg-color').val() || '';
                const linkColor = $('#map-custom-link-color').val() || '';
                const headingColor = $('#map-custom-heading-color').val() || '';

                this.customThemeColors = {
                    text: textColor,
                    background: bgColor,
                    link: linkColor,
                    heading: headingColor
                };

                this.customThemeDebounceTimer = setTimeout(() => {
                    try {
                        this.applyCustomTheme();
                    } catch (error) {
                        // Silent error handling
                    }
                }, 300);
            } catch (error) {
                // Silent error handling
            }
        };

        /**
         * Apply custom theme to the website
         */
        window.MapAccessibility.prototype.applyCustomTheme = function() {
            $('body').removeClass('map-theme-normal map-theme-monochrome map-theme-low-saturation map-theme-high-saturation map-theme-dark map-theme-high-contrast map-theme-sepia map-theme-colorblind');
            $('body').addClass('map-theme-custom');

            const customCSS = `
                <style id="map-custom-theme-styles">
                    body.map-theme-custom {
                        background-color: ${this.customThemeColors.background} !important;
                        color: ${this.customThemeColors.text} !important;
                    }
                    body.map-theme-custom p,
                    body.map-theme-custom div:not(.map-accessibility-widget):not(.map-accessibility-widget *),
                    body.map-theme-custom span:not(.map-accessibility-widget *):not(.map-color-title):not(.map-color-desc),
                    body.map-theme-custom li,
                    body.map-theme-custom td,
                    body.map-theme-custom th {
                        color: ${this.customThemeColors.text} !important;
                    }
                    body.map-theme-custom h1,
                    body.map-theme-custom h2,
                    body.map-theme-custom h3,
                    body.map-theme-custom h4,
                    body.map-theme-custom h5,
                    body.map-theme-custom h6 {
                        color: ${this.customThemeColors.heading} !important;
                    }
                    body.map-theme-custom a {
                        color: ${this.customThemeColors.link} !important;
                    }
                    body.map-theme-custom main,
                    body.map-theme-custom article,
                    body.map-theme-custom section,
                    body.map-theme-custom .content,
                    body.map-theme-custom .post,
                    body.map-theme-custom .page {
                        background-color: ${this.customThemeColors.background} !important;
                    }
                </style>
            `;

            $('#map-custom-theme-styles').remove();
            $('head').append(customCSS);

            this.isCustomThemeActive = true;
            this.currentContrastTheme = 'custom';
            this.saveUserPreferences();
        };

        /**
         * Initialize contrast themes section when colors view becomes active
         */
        window.MapAccessibility.prototype.initializeContrastThemesSection = function() {
            const $toggle = $('#map-contrast-themes-toggle');
            const $content = $('#map-contrast-themes-content');

            if ($toggle.length && $content.length) {
                $toggle.attr('data-active', 'false');
                $toggle.removeClass('active');
                this.initializeThemeSelector();
            }
        };

        /**
         * Initialize the modern theme selector
         */
        window.MapAccessibility.prototype.initializeThemeSelector = function() {
            this.currentThemeIndex = this.themes.findIndex(theme => theme.id === this.currentContrastTheme);
            if (this.currentThemeIndex === -1) this.currentThemeIndex = 0;

            this.selectedTheme = this.currentContrastTheme;
            this.updateThemeSelector();
        };

        /**
         * Navigate to previous/next theme
         * @param {string} direction - 'prev' or 'next'
         */
        window.MapAccessibility.prototype.navigateTheme = function(direction) {
            if (direction === 'prev') {
                this.currentThemeIndex = (this.currentThemeIndex - 1 + this.themes.length) % this.themes.length;
            } else {
                this.currentThemeIndex = (this.currentThemeIndex + 1) % this.themes.length;
            }

            this.selectedTheme = this.themes[this.currentThemeIndex].id;
            this.updateThemeSelector();

            this.applyContrastTheme(this.selectedTheme);
            this.showThemeAppliedFeedback();
        };

        /**
         * Select a specific theme
         * @param {string} themeId - Theme ID to select
         */
        window.MapAccessibility.prototype.selectTheme = function(themeId) {
            const themeIndex = this.themes.findIndex(theme => theme.id === themeId);
            if (themeIndex !== -1) {
                this.currentThemeIndex = themeIndex;
                this.selectedTheme = themeId;
                this.updateThemeSelector();

                this.applyContrastTheme(this.selectedTheme);
                this.showThemeAppliedFeedback();
            }
        };

        /**
         * Navigate theme selector to Default theme (for reset UX)
         */
        window.MapAccessibility.prototype.navigateToDefaultTheme = function() {
            const defaultThemeIndex = this.themes.findIndex(theme => theme.id === 'normal');

            if (defaultThemeIndex !== -1) {
                this.currentThemeIndex = defaultThemeIndex;
                this.selectedTheme = 'normal';
                this.updateThemeSelector();
                this.showThemeNavigationFeedback();
            }
        };

        /**
         * Show visual feedback when navigating to Default theme
         */
        window.MapAccessibility.prototype.showThemeNavigationFeedback = function() {
            const $iconPreview = $('#map-theme-icon-preview');
            if ($iconPreview.length) {
                $iconPreview.addClass('map-theme-feedback');
                setTimeout(() => {
                    $iconPreview.removeClass('map-theme-feedback');
                }, 600);
            }
        };

        /**
         * Update the theme selector UI
         */
        window.MapAccessibility.prototype.updateThemeSelector = function() {
            const currentTheme = this.themes[this.currentThemeIndex];

            const $iconPreview = $('#map-theme-icon-preview');
            $iconPreview.html(currentTheme.icon);
            $iconPreview.attr('data-theme', currentTheme.id);

            $('#map-theme-name').text(currentTheme.name);

            $('.map-theme-dot').removeClass('active');
            $(`.map-theme-dot[data-theme="${currentTheme.id}"]`).addClass('active');
        };

        /**
         * Show visual feedback when theme is applied
         */
        window.MapAccessibility.prototype.showThemeAppliedFeedback = function() {
            const $iconPreview = $('#map-theme-icon-preview');
            $iconPreview.addClass('applying');

            setTimeout(() => {
                $iconPreview.removeClass('applying');
            }, 1000);
        };

        /**
         * Initialize custom theme colors section when colors view becomes active
         */
        window.MapAccessibility.prototype.initializeCustomThemeColorsSection = function() {
            const $toggle = $('#map-custom-theme-toggle');
            const $content = $('#map-custom-theme-content');

            if ($toggle.length && $content.length) {
                this.initializeCustomThemeColors();

                $content.find('.map-color-picker-compact').each((_, element) => {
                    this.toggleResetButtonVisibility($(element));
                });

                $toggle.css({
                    'pointer-events': 'auto',
                    'cursor': 'pointer',
                    'z-index': '10'
                });

                $toggle.off('click.customTheme').on('click.customTheme', (e) => {
                    e.preventDefault();
                    e.stopPropagation();
                    this.toggleCustomThemeSection();
                });

                $content.find('.map-color-picker, .map-color-picker-compact').css({
                    'pointer-events': 'auto',
                    'position': 'relative',
                    'z-index': '2'
                });

                $content.find('.map-color-picker, .map-color-picker-compact').off('input.direct change.direct click.direct').on('input.direct change.direct click.direct', (e) => {
                    this.applyIndividualColorChange($(e.target));
                    this.toggleResetButtonVisibility($(e.target));
                });
            }
        };

        /**
         * Update theme translations
         */
        window.MapAccessibility.prototype.updateThemeTranslations = function() {
            // Update theme names in the themes array
            if (this.themes) {
                this.themes.forEach(theme => {
                    switch(theme.id) {
                        case 'normal':
                            theme.name = this.translate('Default');
                            theme.description = this.translate('Standard website appearance with default colors');
                            break;
                        case 'monochrome':
                            theme.name = this.translate('Monochrome');
                            theme.description = this.translate('Grayscale colors for reduced visual distraction');
                            break;
                        case 'low-saturation':
                            theme.name = this.translate('Low Saturation');
                            theme.description = this.translate('Reduced color intensity for comfortable viewing');
                            break;
                        case 'high-saturation':
                            theme.name = this.translate('High Saturation');
                            theme.description = this.translate('Enhanced color intensity for vibrant viewing');
                            break;
                        case 'dark':
                            theme.name = this.translate('Dark Mode');
                            theme.description = this.translate('Dark background with light text for low-light environments');
                            break;
                        case 'high-contrast':
                            theme.name = this.translate('High Contrast');
                            theme.description = this.translate('Black and white for maximum readability');
                            break;
                        case 'sepia':
                            theme.name = this.translate('Sepia');
                            theme.description = this.translate('Warm, paper-like colors for comfortable reading');
                            break;
                        case 'colorblind':
                            theme.name = this.translate('Color Blind Friendly');
                            theme.description = this.translate('Optimized colors for color vision deficiency');
                            break;
                    }
                });

                // Re-render theme selector if it's visible
                if ($('.map-theme-selector').length > 0) {
                    this.updateThemeSelector();
                }
            }
        };

        /**
         * Toggle dark mode for the accessibility widget interface
         */
        window.MapAccessibility.prototype.toggleDarkMode = function() {
            const $toggle = $('#map-dark-mode-toggle');
            const $widget = $('#map-accessibility-widget');
            const isActive = $toggle.attr('data-active') === 'true';

            if (isActive) {
                // Disable dark mode
                $toggle.attr('data-active', 'false');
                $toggle.removeClass('active');
                $widget.removeClass('map-dark-mode');
                this.isDarkModeActive = false;
            } else {
                // Enable dark mode
                $toggle.attr('data-active', 'true');
                $toggle.addClass('active');
                $widget.addClass('map-dark-mode');
                this.isDarkModeActive = true;
            }

            // Update ADHD Focus colors if active
            this.updateADHDFocusColors();

            // Save preference
            this.saveUserPreferences();
        };

        /**
         * Restore dark mode state
         */
        window.MapAccessibility.prototype.restoreDarkModeState = function() {
            const $toggle = $('#map-dark-mode-toggle');
            const $widget = $('#map-accessibility-widget');

            if (this.isDarkModeActive) {
                $toggle.attr('data-active', 'true');
                $toggle.addClass('active');
                $widget.addClass('map-dark-mode');
            }
        };

        // Visual adjustments methods completed
    }

})(jQuery);
