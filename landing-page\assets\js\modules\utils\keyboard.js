/**
 * Keyboard Module - Keyboard Navigation and Shortcuts
 *
 * This module contains:
 * - Keyboard shortcuts setup
 * - Keyboard navigation for UI elements
 * - Accessibility keyboard support
 * - Focus management utilities
 *
 * @package MyAccessibilityPlugin
 * @since 1.0.0
 */

(function($) {
    'use strict';

    // Extend MapAccessibility with keyboard functionality
    if (typeof window.MapAccessibility !== 'undefined') {

        /**
         * Setup keyboard shortcuts
         */
        window.MapAccessibility.prototype.setupKeyboardShortcuts = function() {
            const self = this;

            $(document).on('keydown.mapAccessibility', function(e) {
                // Alt + A: Toggle widget
                if (e.altKey && e.key.toLowerCase() === 'a') {
                    e.preventDefault();
                    self.toggleWidget();
                }

                // Alt + S: Toggle speech
                if (e.altKey && e.key.toLowerCase() === 's') {
                    e.preventDefault();
                    if ($('#map-widget-panel').is(':visible')) {
                        self.toggleSpeech();
                    }
                }

                // Alt + X: Stop speech
                if (e.altKey && e.key.toLowerCase() === 'x') {
                    e.preventDefault();
                    self.stopSpeech();
                }

                // Escape: Navigate back or close widget
                if (e.key === 'Escape') {
                    if ($('#map-widget-panel').is(':visible')) {
                        e.preventDefault();
                        if (self.currentView !== 'main-menu') {
                            self.showMainMenu();
                        } else {
                            self.closeWidget();
                        }
                    }
                }
            });
        };

        // Additional keyboard methods will be added here
    }

})(jQuery);
