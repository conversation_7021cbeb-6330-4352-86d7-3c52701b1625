/**
 * Reset Module - Category and Global Reset Functionality
 *
 * This module contains:
 * - Category-specific reset functions
 * - Global reset functionality
 * - Reset button management
 * - State cleanup utilities
 *
 * @package MyAccessibilityPlugin
 * @since 1.0.0
 */

(function($) {
    'use strict';

    // Extend MapAccessibility with reset functionality
    if (typeof window.MapAccessibility !== 'undefined') {

        /**
         * Show reset button
         */
        window.MapAccessibility.prototype.showResetButton = function() {
            $('#map-reset-category').show();
        };

        /**
         * Hide reset button
         */
        window.MapAccessibility.prototype.hideResetButton = function() {
            $('#map-reset-category').hide();
        };

        /**
         * Reset current category options to default
         */
        window.MapAccessibility.prototype.resetCurrentCategory = function() {
            if (!this.currentCategory) {
                return;
            }

            switch (this.currentCategory) {
                case 'text':
                    this.resetTextCategory();
                    break;
                case 'colors':
                    this.resetColorsCategory();
                    break;
                case 'navigation':
                    this.resetNavigationCategory();
                    break;
                case 'preferences':
                    this.resetPreferencesCategory();
                    break;
                default:
                    break;
            }
        };

        /**
         * Reset Text category options (now Reading & Cognitive Support)
         */
        window.MapAccessibility.prototype.resetTextCategory = function() {
            // Reset Text-to-Speech
            if (this.isTextToSpeechActive) {
                this.disableTextSelection();
                this.hideFloatingButton();
                this.stopSpeech();
                this.isTextToSpeechActive = false;

                const $ttsToggle = $('#map-tts-toggle');
                if ($ttsToggle.length) {
                    $ttsToggle.attr('data-active', 'false');
                    $ttsToggle.removeClass('active');
                }
            }

            // Reset Dyslexic Font
            if (this.isDyslexicFontActive) {
                this.disableDyslexicFont();
                this.isDyslexicFontActive = false;

                const $dyslexicToggle = $('#map-dyslexic-font-toggle');
                if ($dyslexicToggle.length) {
                    $dyslexicToggle.attr('data-active', 'false');
                    $dyslexicToggle.removeClass('active');
                }
            }

            // Reset Reading Guide
            if (this.isReadingGuideActive) {
                this.disableReadingGuide();
                this.isReadingGuideActive = false;

                const $readingGuideToggle = $('#map-reading-guide-toggle');
                if ($readingGuideToggle.length) {
                    $readingGuideToggle.attr('data-active', 'false');
                    $readingGuideToggle.removeClass('active');
                }
            }

            // Reset ADHD Focus Mode
            if (this.adhdFocusMode) {
                $('body').removeClass('map-adhd-focus-mode');
                $('#map-adhd-focus-overlay, #map-adhd-focus-border').hide();
                $(document).off('mousemove.adhd');
                this.adhdFocusMode = false;

                const $adhdToggle = $('#map-nav-adhd-focus-toggle');
                if ($adhdToggle.length) {
                    $adhdToggle.attr('data-active', 'false');
                    $adhdToggle.removeClass('active');
                }
            }

            // Reset Big Cursor Mode
            if (this.bigCursorMode) {
                $('body').removeClass('map-big-cursor-mode');
                this.bigCursorMode = false;

                const $bigCursorToggle = $('#map-big-cursor-toggle');
                if ($bigCursorToggle.length) {
                    $bigCursorToggle.attr('data-active', 'false');
                    $bigCursorToggle.removeClass('active');
                }
            }

            this.saveUserPreferences();
        };

        /**
         * Reset Navigation category options (now Text & Typography)
         */
        window.MapAccessibility.prototype.resetNavigationCategory = function() {
            // Reset Text Magnification Mode
            if (this.textMagnificationMode) {
                this.disableTextMagnification();
                this.textMagnificationMode = false;

                const $textMagnificationToggle = $('#map-text-magnification-toggle');
                if ($textMagnificationToggle.length) {
                    $textMagnificationToggle.attr('data-active', 'false');
                    $textMagnificationToggle.removeClass('active');
                }
            }

            // Reset Font Size
            this.currentFontSize = 100;
            this.applyFontSize();
            this.updateFontSizeDisplay();
            this.updateFontSizeButtons();

            // Hide font size controls and reset toggle state
            const $fontSizeControls = $('#map-font-size-controls');
            if ($fontSizeControls.length) {
                $fontSizeControls.hide();
            }

            const $fontSizeToggle = $('#map-font-size-toggle');
            if ($fontSizeToggle.length) {
                $fontSizeToggle.attr('data-active', 'false');
                $fontSizeToggle.removeClass('active');
                this.toggleNavigationArrow($fontSizeToggle);
            }

            // Reset Line Spacing
            this.currentLineSpacing = 1.5;
            this.applyLineSpacing();
            this.updateLineSpacingDisplay();
            this.updateLineSpacingSlider();

            // Hide line spacing controls and reset toggle state
            const $lineSpacingControls = $('#map-line-spacing-controls');
            if ($lineSpacingControls.length) {
                $lineSpacingControls.hide();
            }

            const $lineSpacingToggle = $('#map-line-spacing-toggle');
            if ($lineSpacingToggle.length) {
                $lineSpacingToggle.attr('data-active', 'false');
                $lineSpacingToggle.removeClass('active');
                this.toggleNavigationArrow($lineSpacingToggle);
            }

            // Reset Letter Spacing
            this.currentLetterSpacing = 0;
            this.applyLetterSpacing();
            this.updateLetterSpacingDisplay();
            this.updateLetterSpacingSlider();

            // Hide letter spacing controls and reset toggle state
            const $letterSpacingControls = $('#map-letter-spacing-controls');
            if ($letterSpacingControls.length) {
                $letterSpacingControls.hide();
            }

            const $letterSpacingToggle = $('#map-letter-spacing-toggle');
            if ($letterSpacingToggle.length) {
                $letterSpacingToggle.attr('data-active', 'false');
                $letterSpacingToggle.removeClass('active');
                this.toggleNavigationArrow($letterSpacingToggle);
            }

            // Reset Text Alignment
            this.currentTextAlignment = 'left';
            this.applyTextAlignment();
            this.updateTextAlignmentDisplay();
            this.updateTextAlignmentButtons();

            // Hide text alignment controls and reset toggle state
            const $textAlignmentControls = $('#map-text-alignment-controls');
            if ($textAlignmentControls.length) {
                $textAlignmentControls.hide();
            }

            const $textAlignmentToggle = $('#map-text-alignment-toggle');
            if ($textAlignmentToggle.length) {
                $textAlignmentToggle.attr('data-active', 'false');
                $textAlignmentToggle.removeClass('active');
                this.toggleNavigationArrow($textAlignmentToggle);
            }

            this.saveUserPreferences();
        };

        /**
         * Reset Colors category options (now Visual Adjustments)
         */
        window.MapAccessibility.prototype.resetColorsCategory = function() {
            // Reset Dark Mode to light mode
            if (this.isDarkModeActive) {
                const $toggle = $('#map-dark-mode-toggle');
                const $widget = $('#map-accessibility-widget');

                $toggle.attr('data-active', 'false');
                $toggle.removeClass('active');
                $widget.removeClass('map-dark-mode');
                this.isDarkModeActive = false;

                this.updateADHDFocusColors();
            }

            // Reset Contrast Theme to normal
            this.currentContrastTheme = 'normal';
            this.applyContrastTheme('normal');

            // Navigate theme selector to "Default" for better UX
            this.navigateToDefaultTheme();

            // Clear custom theme colors
            this.customThemeColors = {
                text: '',
                background: '',
                link: '',
                heading: ''
            };

            // Clear color pickers
            $('#map-custom-text-color').val('');
            $('#map-custom-bg-color').val('');
            $('#map-custom-link-color').val('');
            $('#map-custom-heading-color').val('');

            // Hide all color reset X buttons
            $('.map-color-reset-btn').removeClass('visible').hide();

            // Remove custom theme from website
            $('body').removeClass('map-theme-custom');
            $('#map-custom-theme-styles').remove();
            this.isCustomThemeActive = false;

            // Clear custom theme debounce timer
            if (this.customThemeDebounceTimer) {
                clearTimeout(this.customThemeDebounceTimer);
                this.customThemeDebounceTimer = null;
            }

            // Remove custom theme from localStorage
            try {
                localStorage.removeItem('map_custom_theme_colors');
                localStorage.removeItem('map_custom_theme_active');
            } catch (e) {
                // Silent error handling
            }

            // Update theme selector UI if it exists
            if ($('#map-theme-preview').length) {
                this.initializeThemeSelector();
            }

            this.saveUserPreferences();
            this.showResetSuccessMessage();
        };

        /**
         * Reset Preferences category options
         */
        window.MapAccessibility.prototype.resetPreferencesCategory = function() {
            // Reset Menu Position to bottom-right
            this.currentMenuPosition = 'bottom-right';
            this.applyMenuPosition('bottom-right');

            $('#map-menu-position-content .map-language-option').removeClass('active map-language-active');
            $('#map-menu-position-content .map-language-option[data-position="bottom-right"]').addClass('active map-language-active');

            // Reset Language to English
            if (this.currentLanguage !== 'en') {
                this.currentLanguage = 'en';

                this.loadLanguage('en').then(() => {
                    this.applyTranslations();
                    this.initializeLanguageState();
                }).catch(error => {
                    console.warn('Failed to load English language during reset:', error);
                    this.initializeLanguageState();
                });
            }

            this.saveUserPreferences();
            this.showResetSuccessMessage();
        };

        /**
         * Show reset success message - Disabled for cleaner UX
         */
        window.MapAccessibility.prototype.showResetSuccessMessage = function() {
            // Success messages disabled to keep interface clean
            return;
        };

        /**
         * Reset all internal states
         */
        window.MapAccessibility.prototype.resetAllInternalStates = function() {
            // Reset all properties to defaults
            this.currentFontSize = 100;
            this.currentLineSpacing = 1.5;
            this.currentContrastTheme = 'normal';
            this.isCustomThemeActive = false;
            this.customThemeColors = {
                text: '',
                background: '',
                link: '',
                heading: ''
            };
            this.isDyslexicFontActive = false;
            this.isReadingGuideActive = false;
            this.isTextToSpeechActive = false;
            this.adhdFocusMode = false;
            this.bigCursorMode = false;
            this.textMagnificationMode = false;
            this.isDarkModeActive = false;
            this.currentMenuPosition = 'bottom-right';
            this.currentLanguage = 'en';

            // Navigate theme selector to Default for better UX
            this.navigateToDefaultTheme();
        };

        /**
         * Show reset confirmation message
         */
        window.MapAccessibility.prototype.showResetConfirmation = function() {
            // Create or show a temporary confirmation message
            let $confirmation = $('#map-reset-confirmation');
            if (!$confirmation.length) {
                $confirmation = $('<div id="map-reset-confirmation" class="map-reset-confirmation">✓ Category options have been reset to default</div>');
                $('.map-panel-content').prepend($confirmation);
            }

            $confirmation.show().addClass('map-reset-confirmation-show');

            // Hide after 3 seconds
            setTimeout(() => {
                $confirmation.removeClass('map-reset-confirmation-show');
                setTimeout(() => {
                    $confirmation.hide();
                }, 300);
            }, 3000);
        };

        // Reset methods will be added here
    }

})(jQuery);
