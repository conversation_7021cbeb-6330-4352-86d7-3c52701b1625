/* Landing Page Styles - Matching Accessibility Plugin Design System */

/* CSS Variables matching the plugin's design system */
:root {
    /* Primary Brand Colors - Matching Plugin */
    --map-primary: #7c3aed;
    --map-primary-light: #8b5cf6;
    --map-primary-dark: #6d28d9;
    --map-accent: #fbbf24;
    --map-accent-light: #fcd34d;

    /* Dark Mode Palette */
    --map-white: #0f0f0f;
    --map-gray-50: #1a1a1a;
    --map-gray-100: #262626;
    --map-gray-200: #333333;
    --map-gray-300: #404040;
    --map-gray-400: #525252;
    --map-gray-500: #737373;
    --map-gray-600: #a3a3a3;
    --map-gray-700: #d4d4d4;
    --map-gray-800: #e5e5e5;
    --map-gray-900: #ffffff;

    /* Semantic Colors */
    --map-success: #10b981;
    --map-warning: #f59e0b;
    --map-error: #ef4444;
    --map-info: #3b82f6;

    /* Design Tokens */
    --map-radius-sm: 6px;
    --map-radius-md: 12px;
    --map-radius-lg: 16px;
    --map-radius-xl: 24px;

    /* Shadows */
    --map-shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.3);
    --map-shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.4), 0 2px 4px -1px rgba(0, 0, 0, 0.2);
    --map-shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.5), 0 4px 6px -2px rgba(0, 0, 0, 0.3);
    --map-shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.6), 0 10px 10px -5px rgba(0, 0, 0, 0.4);

    /* Glow Effects */
    --map-glow-primary: 0 0 20px rgba(124, 58, 237, 0.3);
    --map-glow-accent: 0 0 20px rgba(251, 191, 36, 0.3);

    /* Typography */
    --map-font-size-xs: 0.75rem;
    --map-font-size-sm: 0.875rem;
    --map-font-size-base: 1rem;
    --map-font-size-lg: 1.125rem;
    --map-font-size-xl: 1.25rem;
    --map-font-size-2xl: 1.5rem;
    --map-font-size-3xl: 1.875rem;
    --map-font-size-4xl: 2.25rem;

    /* Spacing */
    --map-space-1: 0.25rem;
    --map-space-2: 0.5rem;
    --map-space-3: 0.75rem;
    --map-space-4: 1rem;
    --map-space-6: 1.5rem;
    --map-space-8: 2rem;
    --map-space-12: 3rem;
    --map-space-16: 4rem;
    --map-space-20: 5rem;
}

/* Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
    background: var(--map-gray-50);
    color: var(--map-gray-900);
    line-height: 1.6;
    overflow-x: hidden;
}

/* Container */
.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 var(--map-space-6);
}

/* Header */
.header {
    background: linear-gradient(135deg, var(--map-gray-50) 0%, var(--map-gray-100) 100%);
    border-bottom: 1px solid var(--map-gray-300);
    padding: var(--map-space-4) 0;
    position: sticky;
    top: 0;
    z-index: 100;
    backdrop-filter: blur(10px);
    transition: background 0.3s ease;
}

.nav {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.logo {
    display: flex;
    align-items: center;
    gap: var(--map-space-3);
    font-size: var(--map-font-size-xl);
    font-weight: 700;
    color: var(--map-gray-900);
    text-decoration: none;
}

.logo-icon {
    width: 40px;
    height: 40px;
    background: linear-gradient(135deg, var(--map-primary) 0%, var(--map-primary-light) 100%);
    border-radius: var(--map-radius-md);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    box-shadow: var(--map-shadow-md), var(--map-glow-primary);
}

.nav-links {
    display: flex;
    gap: var(--map-space-8);
    list-style: none;
}

.nav-links a {
    color: var(--map-gray-700);
    text-decoration: none;
    font-weight: 500;
    transition: color 0.3s ease;
}

.nav-links a:hover {
    color: var(--map-primary);
}

.cta-button {
    background: linear-gradient(135deg, var(--map-primary) 0%, var(--map-primary-light) 100%);
    color: white;
    padding: var(--map-space-3) var(--map-space-6);
    border-radius: var(--map-radius-md);
    text-decoration: none;
    font-weight: 600;
    box-shadow: var(--map-shadow-md), var(--map-glow-primary);
    transition: all 0.3s ease;
    border: none;
    cursor: pointer;
}

.cta-button:hover {
    transform: translateY(-2px);
    box-shadow: var(--map-shadow-lg), var(--map-glow-primary);
}

/* Hero Section */
.hero {
    padding: var(--map-space-20) 0;
    background: linear-gradient(135deg, var(--map-gray-50) 0%, var(--map-gray-100) 50%, var(--map-gray-50) 100%);
    position: relative;
    overflow: hidden;
}

.hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(circle at 30% 20%, rgba(124, 58, 237, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 70% 80%, rgba(251, 191, 36, 0.1) 0%, transparent 50%);
    pointer-events: none;
}

.hero-content {
    text-align: center;
    position: relative;
    z-index: 2;
}

.hero-title {
    font-size: var(--map-font-size-4xl);
    font-weight: 700;
    margin-bottom: var(--map-space-6);
    background: linear-gradient(135deg, var(--map-gray-900) 0%, var(--map-gray-700) 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.hero-subtitle {
    font-size: var(--map-font-size-xl);
    color: var(--map-gray-600);
    margin-bottom: var(--map-space-8);
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

.hero-buttons {
    display: flex;
    gap: var(--map-space-4);
    justify-content: center;
    flex-wrap: wrap;
}

.btn-secondary {
    background: var(--map-gray-200);
    color: var(--map-gray-800);
    padding: var(--map-space-3) var(--map-space-6);
    border-radius: var(--map-radius-md);
    text-decoration: none;
    font-weight: 600;
    box-shadow: var(--map-shadow-sm);
    transition: all 0.3s ease;
    border: 1px solid var(--map-gray-300);
}

.btn-secondary:hover {
    background: var(--map-gray-300);
    transform: translateY(-2px);
    box-shadow: var(--map-shadow-md);
}

/* Features Section */
.features {
    padding: var(--map-space-20) 0;
    background: var(--map-gray-100);
}

.section-title {
    text-align: center;
    font-size: var(--map-font-size-3xl);
    font-weight: 700;
    margin-bottom: var(--map-space-4);
    color: var(--map-gray-900);
}

.section-subtitle {
    text-align: center;
    font-size: var(--map-font-size-lg);
    color: var(--map-gray-600);
    margin-bottom: var(--map-space-16);
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

.features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: var(--map-space-8);
}

.feature-card {
    background: var(--map-gray-50);
    padding: var(--map-space-8);
    border-radius: var(--map-radius-lg);
    box-shadow: var(--map-shadow-md);
    border: 1px solid var(--map-gray-200);
    transition: all 0.3s ease;
}

.feature-card:hover {
    transform: translateY(-4px);
    box-shadow: var(--map-shadow-lg);
}

.feature-icon {
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, var(--map-primary) 0%, var(--map-primary-light) 100%);
    border-radius: var(--map-radius-lg);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: var(--map-font-size-2xl);
    margin-bottom: var(--map-space-6);
    box-shadow: var(--map-shadow-md), var(--map-glow-primary);
}

.feature-title {
    font-size: var(--map-font-size-xl);
    font-weight: 600;
    margin-bottom: var(--map-space-3);
    color: var(--map-gray-900);
}

.feature-description {
    color: var(--map-gray-600);
    line-height: 1.7;
}

/* Demo Section Placeholder */
.demo-section {
    padding: var(--map-space-20) 0;
    background: var(--map-gray-50);
    text-align: center;
}

.demo-placeholder {
    background: var(--map-gray-200);
    border: 2px dashed var(--map-gray-400);
    border-radius: var(--map-radius-lg);
    padding: var(--map-space-16);
    margin: var(--map-space-8) 0;
}

.demo-placeholder h3 {
    color: var(--map-gray-600);
    margin-bottom: var(--map-space-4);
}

/* Pricing Section */
.pricing {
    padding: var(--map-space-20) 0;
    background: var(--map-gray-50);
}

.pricing-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: var(--map-space-8);
    max-width: 1000px;
    margin: 0 auto;
}

.pricing-card {
    background: var(--map-gray-100);
    border-radius: var(--map-radius-lg);
    padding: var(--map-space-8);
    box-shadow: var(--map-shadow-md);
    border: 1px solid var(--map-gray-200);
    position: relative;
    transition: all 0.3s ease;
}

.pricing-card:hover {
    transform: translateY(-4px);
    box-shadow: var(--map-shadow-lg);
}

.pricing-card.featured {
    border: 2px solid var(--map-primary);
    box-shadow: var(--map-shadow-lg), var(--map-glow-primary);
}

.pricing-badge {
    position: absolute;
    top: -12px;
    left: 50%;
    transform: translateX(-50%);
    background: linear-gradient(135deg, var(--map-primary) 0%, var(--map-primary-light) 100%);
    color: white;
    padding: var(--map-space-2) var(--map-space-4);
    border-radius: var(--map-radius-md);
    font-size: var(--map-font-size-sm);
    font-weight: 600;
    box-shadow: var(--map-shadow-md);
}

.pricing-header {
    text-align: center;
    margin-bottom: var(--map-space-6);
}

.pricing-title {
    font-size: var(--map-font-size-xl);
    font-weight: 600;
    color: var(--map-gray-900);
    margin-bottom: var(--map-space-4);
}

.pricing-price {
    display: flex;
    align-items: baseline;
    justify-content: center;
    margin-bottom: var(--map-space-2);
}

.currency {
    font-size: var(--map-font-size-xl);
    color: var(--map-gray-600);
}

.amount {
    font-size: 3rem;
    font-weight: 700;
    color: var(--map-primary);
}

.pricing-period {
    color: var(--map-gray-600);
    font-size: var(--map-font-size-sm);
}

.pricing-features {
    list-style: none;
    margin-bottom: var(--map-space-8);
}

.pricing-features li {
    display: flex;
    align-items: center;
    gap: var(--map-space-3);
    padding: var(--map-space-2) 0;
    color: var(--map-gray-700);
}

.pricing-features i {
    color: var(--map-success);
    font-size: var(--map-font-size-sm);
}

.pricing-button {
    width: 100%;
    background: linear-gradient(135deg, var(--map-primary) 0%, var(--map-primary-light) 100%);
    color: white;
    padding: var(--map-space-4);
    border-radius: var(--map-radius-md);
    text-decoration: none;
    font-weight: 600;
    text-align: center;
    display: block;
    box-shadow: var(--map-shadow-md), var(--map-glow-primary);
    transition: all 0.3s ease;
}

.pricing-button:hover {
    transform: translateY(-2px);
    box-shadow: var(--map-shadow-lg), var(--map-glow-primary);
}

/* Footer */
.footer {
    background: var(--map-gray-100);
    border-top: 1px solid var(--map-gray-300);
    padding: var(--map-space-16) 0 var(--map-space-8);
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--map-space-8);
    margin-bottom: var(--map-space-8);
}

.footer-logo {
    display: flex;
    align-items: center;
    gap: var(--map-space-3);
    font-size: var(--map-font-size-lg);
    font-weight: 600;
    color: var(--map-gray-900);
    margin-bottom: var(--map-space-4);
}

.footer-description {
    color: var(--map-gray-600);
    line-height: 1.7;
}

.footer-title {
    font-size: var(--map-font-size-lg);
    font-weight: 600;
    color: var(--map-gray-900);
    margin-bottom: var(--map-space-4);
}

.footer-links {
    list-style: none;
}

.footer-links li {
    margin-bottom: var(--map-space-2);
}

.footer-links a {
    color: var(--map-gray-600);
    text-decoration: none;
    transition: color 0.3s ease;
}

.footer-links a:hover {
    color: var(--map-primary);
}

.footer-bottom {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-top: var(--map-space-8);
    border-top: 1px solid var(--map-gray-300);
    color: var(--map-gray-600);
}

.footer-social {
    display: flex;
    gap: var(--map-space-4);
}

.footer-social a {
    width: 40px;
    height: 40px;
    background: var(--map-gray-200);
    border-radius: var(--map-radius-md);
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--map-gray-600);
    text-decoration: none;
    transition: all 0.3s ease;
}

.footer-social a:hover {
    background: var(--map-primary);
    color: white;
    transform: translateY(-2px);
}

/* Responsive Design */
@media (max-width: 768px) {
    .nav-links {
        display: none;
    }

    .hero-title {
        font-size: var(--map-font-size-3xl);
    }

    .hero-buttons {
        flex-direction: column;
        align-items: center;
    }

    .features-grid {
        grid-template-columns: 1fr;
    }

    .pricing-grid {
        grid-template-columns: 1fr;
    }

    .footer-content {
        grid-template-columns: 1fr;
        text-align: center;
    }

    .footer-bottom {
        flex-direction: column;
        gap: var(--map-space-4);
        text-align: center;
    }
}

@media (max-width: 480px) {
    .container {
        padding: 0 var(--map-space-4);
    }

    .hero {
        padding: var(--map-space-16) 0;
    }

    .features, .pricing, .demo-section {
        padding: var(--map-space-16) 0;
    }

    .feature-card, .pricing-card {
        padding: var(--map-space-6);
    }
}
