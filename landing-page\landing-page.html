<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>My Accessibility Plugin - Premium WordPress Accessibility Solution</title>
    <meta name="description" content="Transform your WordPress website with our premium accessibility plugin. Features text-to-speech, contrast themes, dyslexic fonts, and more.">
    
    <!-- Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- Icons -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">

    <!-- Custom Styles -->
    <link rel="stylesheet" href="landing-page.css">

    <!-- Accessibility Plugin CSS Files -->
    <link rel="stylesheet" href="assets/css/variables.css">
    <link rel="stylesheet" href="assets/css/widget-core.css">
    <link rel="stylesheet" href="assets/css/ui-components.css">
    <link rel="stylesheet" href="assets/css/modal-system.css">
    <link rel="stylesheet" href="assets/css/category-menu.css">
    <link rel="stylesheet" href="assets/css/reading-cognitive-features.css">
    <link rel="stylesheet" href="assets/css/visual-adjustment-features.css">
    <link rel="stylesheet" href="assets/css/text-typography-features.css">
    <link rel="stylesheet" href="assets/css/settings.css">
    <link rel="stylesheet" href="assets/css/themes.css">
    <link rel="stylesheet" href="assets/css/animations.css">
    <link rel="stylesheet" href="assets/css/responsive.css">
    <link rel="stylesheet" href="assets/css/utilities.css">

    <!-- Custom styles for widget positioning -->
    <style id="map-widget-custom-styles">
        :root {
            --map-primary-color: #0073aa;
            --map-primary-hover: #005a87;
            --map-primary-active: #004a6b;
        }



        /* Fix aria-hidden visibility issue - Essential working fix */
        #map-widget-panel {
            display: flex !important;
            visibility: visible !important;
            z-index: 999999 !important;
        }

        /* Hide panel only when explicitly closed */
        .map-accessibility-widget:not(.map-panel-active) #map-widget-panel {
            display: none !important;
        }

    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <nav class="nav container">
            <a href="#" class="logo">
                <div class="logo-icon">
                    <i class="fas fa-universal-access"></i>
                </div>
                My Accessibility Plugin
            </a>
            
            <ul class="nav-links">
                <li><a href="#features">Features</a></li>
                <li><a href="#demo">Live Demo</a></li>
                <li><a href="#pricing">Pricing</a></li>
                <li><a href="#support">Support</a></li>
            </ul>
        </nav>
    </header>

    <!-- Hero Section -->
    <section class="hero">
        <div class="container">
            <div class="hero-content">
                <h1 class="hero-title">
                    Make Your WordPress Site Accessible to Everyone
                </h1>
                <p class="hero-subtitle">
                    Transform your website with our premium accessibility plugin featuring text-to-speech, 
                    contrast themes, dyslexic fonts, and comprehensive accessibility tools.
                </p>
                <div class="hero-buttons">
                    <a href="#demo" class="cta-button">
                        <i class="fas fa-play"></i>
                        Try Live Demo
                    </a>
                    <a href="#features" class="btn-secondary">
                        <i class="fas fa-list"></i>
                        View Features
                    </a>
                </div>
            </div>
        </div>
    </section>

    <!-- Features Section -->
    <section id="features" class="features">
        <div class="container">
            <h2 class="section-title">Powerful Accessibility Features</h2>
            <p class="section-subtitle">
                Everything you need to make your WordPress website accessible and inclusive for all users.
            </p>
            
            <div class="features-grid">
                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-volume-up"></i>
                    </div>
                    <h3 class="feature-title">Text-to-Speech</h3>
                    <p class="feature-description">
                        Advanced text-to-speech functionality with customizable voice settings, 
                        reading speed control, and multi-language support for enhanced accessibility.
                    </p>
                </div>
                
                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-palette"></i>
                    </div>
                    <h3 class="feature-title">Contrast Themes</h3>
                    <p class="feature-description">
                        Multiple contrast themes including dark mode, high contrast, and colorblind-friendly 
                        options to improve readability for all users.
                    </p>
                </div>
                
                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-font"></i>
                    </div>
                    <h3 class="feature-title">Dyslexic Font Support</h3>
                    <p class="feature-description">
                        OpenDyslexic font integration with customizable font sizes and spacing 
                        to help users with dyslexia read more comfortably.
                    </p>
                </div>
                
                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-mouse-pointer"></i>
                    </div>
                    <h3 class="feature-title">Navigation Assistance</h3>
                    <p class="feature-description">
                        Enhanced navigation tools including focus indicators, keyboard navigation, 
                        and ADHD-friendly reading guides for better user experience.
                    </p>
                </div>
                
                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-cog"></i>
                    </div>
                    <h3 class="feature-title">Customizable Settings</h3>
                    <p class="feature-description">
                        Comprehensive admin panel with easy-to-use settings, user preferences 
                        storage, and seamless WordPress integration.
                    </p>
                </div>
                
                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-mobile-alt"></i>
                    </div>
                    <h3 class="feature-title">Fully Responsive</h3>
                    <p class="feature-description">
                        Mobile-first design that works perfectly on all devices and screen sizes, 
                        ensuring accessibility features are available everywhere.
                    </p>
                </div>
            </div>
        </div>
    </section>

    <!-- Demo Section Placeholder -->
    <section id="demo" class="demo-section">
        <div class="container">
            <h2 class="section-title">Live Demo</h2>
            <p class="section-subtitle">
                Experience the plugin in action with our interactive demo.
            </p>

            <div class="demo-placeholder">
                <h3><i class="fas fa-play-circle"></i> Interactive Demo Coming Soon</h3>
                <p>This section will contain the live demo of the accessibility plugin.</p>
            </div>
        </div>
    </section>

    <!-- Pricing Section -->
    <section id="pricing" class="pricing">
        <div class="container">
            <h2 class="section-title">Simple, Transparent Pricing</h2>
            <p class="section-subtitle">
                Choose the perfect plan for your WordPress website needs.
            </p>

            <div class="pricing-grid">
                <div class="pricing-card">
                    <div class="pricing-header">
                        <h3 class="pricing-title">Single Site</h3>
                        <div class="pricing-price">
                            <span class="currency">$</span>
                            <span class="amount">49</span>
                        </div>
                        <p class="pricing-period">One-time payment</p>
                    </div>
                    <ul class="pricing-features">
                        <li><i class="fas fa-check"></i> 1 WordPress Site</li>
                        <li><i class="fas fa-check"></i> All Premium Features</li>
                        <li><i class="fas fa-check"></i> 6 Months Support</li>
                        <li><i class="fas fa-check"></i> Regular Updates</li>
                        <li><i class="fas fa-check"></i> Documentation</li>
                    </ul>
                    <a href="#" class="pricing-button">Get Started</a>
                </div>

                <div class="pricing-card featured">
                    <div class="pricing-badge">Most Popular</div>
                    <div class="pricing-header">
                        <h3 class="pricing-title">Developer</h3>
                        <div class="pricing-price">
                            <span class="currency">$</span>
                            <span class="amount">99</span>
                        </div>
                        <p class="pricing-period">One-time payment</p>
                    </div>
                    <ul class="pricing-features">
                        <li><i class="fas fa-check"></i> Unlimited Sites</li>
                        <li><i class="fas fa-check"></i> All Premium Features</li>
                        <li><i class="fas fa-check"></i> 12 Months Support</li>
                        <li><i class="fas fa-check"></i> Priority Updates</li>
                        <li><i class="fas fa-check"></i> Advanced Documentation</li>
                        <li><i class="fas fa-check"></i> White Label Rights</li>
                    </ul>
                    <a href="#" class="pricing-button">Get Started</a>
                </div>

                <div class="pricing-card">
                    <div class="pricing-header">
                        <h3 class="pricing-title">Extended</h3>
                        <div class="pricing-price">
                            <span class="currency">$</span>
                            <span class="amount">199</span>
                        </div>
                        <p class="pricing-period">One-time payment</p>
                    </div>
                    <ul class="pricing-features">
                        <li><i class="fas fa-check"></i> Unlimited Sites</li>
                        <li><i class="fas fa-check"></i> All Premium Features</li>
                        <li><i class="fas fa-check"></i> Lifetime Support</li>
                        <li><i class="fas fa-check"></i> Priority Updates</li>
                        <li><i class="fas fa-check"></i> Source Code Access</li>
                        <li><i class="fas fa-check"></i> Custom Development</li>
                    </ul>
                    <a href="#" class="pricing-button">Get Started</a>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <div class="footer-logo">
                        <div class="logo-icon">
                            <i class="fas fa-universal-access"></i>
                        </div>
                        <span>My Accessibility Plugin</span>
                    </div>
                    <p class="footer-description">
                        Making the web accessible for everyone with premium WordPress accessibility solutions.
                    </p>
                </div>

                <div class="footer-section">
                    <h4 class="footer-title">Product</h4>
                    <ul class="footer-links">
                        <li><a href="#features">Features</a></li>
                        <li><a href="#demo">Live Demo</a></li>
                        <li><a href="#pricing">Pricing</a></li>
                        <li><a href="#">Documentation</a></li>
                    </ul>
                </div>

                <div class="footer-section">
                    <h4 class="footer-title">Support</h4>
                    <ul class="footer-links">
                        <li><a href="#">Help Center</a></li>
                        <li><a href="#">Contact Us</a></li>
                        <li><a href="#">Bug Reports</a></li>
                        <li><a href="#">Feature Requests</a></li>
                    </ul>
                </div>

                <div class="footer-section">
                    <h4 class="footer-title">Legal</h4>
                    <ul class="footer-links">
                        <li><a href="#">Privacy Policy</a></li>
                        <li><a href="#">Terms of Service</a></li>
                        <li><a href="#">License</a></li>
                        <li><a href="#">Refund Policy</a></li>
                    </ul>
                </div>
            </div>

            <div class="footer-bottom">
                <p>&copy; 2024 My Accessibility Plugin. All rights reserved.</p>
                <div class="footer-social">
                    <a href="#"><i class="fab fa-twitter"></i></a>
                    <a href="#"><i class="fab fa-facebook"></i></a>
                    <a href="#"><i class="fab fa-linkedin"></i></a>
                    <a href="#"><i class="fab fa-github"></i></a>
                </div>
            </div>
        </div>
    </footer>

    <!-- Accessibility Widget -->
    <div id="map-accessibility-widget" class="map-accessibility-widget map-position-bottom-right map-style-modern map-size-medium" role="region" aria-label="Accessibility Tools">

        <!-- Main Toggle Button -->
        <button id="map-main-toggle" class="map-main-toggle" type="button" aria-expanded="false" aria-controls="map-widget-panel" aria-label="Open accessibility tools">
            <span class="map-toggle-icon" aria-hidden="true">
                <svg width="32" height="32" viewBox="0 0 24 24" fill="currentColor">
                    <path d="M12 2C13.1 2 14 2.9 14 4C14 5.1 13.1 6 12 6C10.9 6 10 5.1 10 4C10 2.9 10.9 2 12 2M12 8C13.1 8 14 8.9 14 10C14 11.1 13.1 12 12 12C10.9 12 10 11.1 10 10C10 8.9 10.9 8 12 8M12 14C13.1 14 14 14.9 14 16C14 17.1 13.1 18 12 18C10.9 18 10 17.1 10 16C10 14.9 10.9 14 12 14M6 8C7.1 8 8 8.9 8 10C8 11.1 7.1 12 6 12C4.9 12 4 11.1 4 10C4 8.9 4.9 8 6 8M18 8C19.1 8 20 8.9 20 10C20 11.1 19.1 12 18 12C16.9 12 16 11.1 16 10C16 8.9 16.9 8 18 8M6 14C7.1 14 8 14.9 8 16C8 17.1 7.1 18 6 18C4.9 18 4 17.1 4 16C4 14.9 4.9 14 6 14M18 14C19.1 14 20 14.9 20 16C20 17.1 19.1 18 18 18C16.9 18 16 17.1 16 16C16 14.9 16.9 14 18 14"/>
                </svg>
            </span>
        </button>

        <!-- Widget Panel -->
        <div id="map-widget-panel" class="map-widget-panel" style="display: none;" aria-hidden="true">
            <div class="map-panel-header">
                <!-- Main title - shown when on main menu -->
                <h3 id="map-panel-title" class="map-panel-title">Accessibility Tools</h3>

                <!-- Back button with category title - shown when in category views -->
                <div id="map-header-navigation" class="map-header-navigation" style="display: none;">
                    <button id="map-header-back-button" class="map-header-back-button" type="button" aria-label="Back to main menu">
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M15.41 16.59L10.83 12l4.58-4.59L14 6l-6 6 6 6 1.41-1.41z"/>
                        </svg>
                    </button>
                    <h4 id="map-header-category-title" class="map-header-category-title"></h4>
                </div>

                <div class="map-header-buttons">
                    <button id="map-reset-category" class="map-reset-button" type="button" aria-label="Reset category options to default" style="display: none;">
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M17.65 6.35C16.2 4.9 14.21 4 12 4c-4.42 0-7.99 3.58-7.99 8s3.57 8 7.99 8c3.73 0 6.84-2.55 7.73-6h-2.08c-.82 2.33-3.04 4-5.65 4-3.31 0-6-2.69-6-6s2.69-6 6-6c1.66 0 3.14.69 4.22 1.78L13 11h7V4l-2.35 2.35z"/>
                        </svg>
                        <span>Reset</span>
                    </button>
                    <button id="map-close-panel" class="map-close-button" type="button" aria-label="Close accessibility tools">
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"/>
                        </svg>
                    </button>
                </div>
            </div>

            <div class="map-panel-content">
                <!-- Premium Animation Container for Modal Views -->
                <div class="map-modal-views-container">
                    <!-- Main Menu (Level 1) - Category Selection -->
                <div id="map-main-menu" class="map-modal-view map-modal-view-active" role="menu" aria-label="Accessibility categories">
                    <div class="map-category-grid">
                        <!-- Reading & cognitive support Category -->
                        <button id="map-category-text" class="map-category-button" type="button" role="menuitem" data-category="text" aria-describedby="map-category-text-desc">
                            <div class="map-category-icon map-category-icon-text">
                                        <svg xmlns="http://www.w3.org/2000/svg" shape-rendering="geometricPrecision" text-rendering="geometricPrecision" image-rendering="optimizeQuality" fill-rule="evenodd" clip-rule="evenodd" viewBox="0 0 418 512.535" stroke="white" fill="currentColor"  width="30" height="30" stroke-width="12">
                            <path fill-rule="nonzero" d="M40.896 237.026c.444-78.78.889-144.007 1.336-222.478.018-5.233 3.732-9.586 8.658-10.603l-.004-.015c53.592-11.186 123.886.624 158.684 46.863C246.233 5.818 313.831-4.137 368.173 3.804c5.425.795 9.33 5.457 9.325 10.785.02 0 .011 205.116.011 223.403 0 6.029-4.887 10.916-10.915 10.916-.871 0-1.718-.105-2.527-.294-20.718-3.267-48.53-1.344-70.273 4.484l-.181 20.595c3.1-.091 6.2-.125 9.3-.106 27.864.177 65.683 4.722 93.256 11.83 0-82.659-.011-151.791-.011-234.435h10.927c6.027 0 10.915 4.887 10.915 10.916v237.8c0 6.028-4.888 10.916-10.915 10.916-1.231 0-2.414-.205-3.519-.58-24.055-6.888-54.318-11.953-80.722-13.867 1.621 2.518 2.854 5.38 3.784 8.476h13.992c.563 0 1.111.065 1.636.189 12.586 2.019 18.453 10.811 21.008 22.01a7.141 7.141 0 011.896-.255h9.848c.564 0 1.113.066 1.64.191 27.033 4.324 26.638 39.142 26.379 61.855-.008.761-.017 1.479-.017 5.486l.565 25.171c.016.263.019.529.007.799-1.209 24.503-12.31 43.636-23.751 63.356l-.969 1.692c-5.047 8.826-10.9 15.567-17.568 20.177-6.788 4.692-14.297 7.104-22.538 7.192l-.652.029h-84.431v-.024c-8.493.169-15.088-3.546-19.937-10.816-3.904-5.848-6.499-14.044-7.898-24.372l-53.319-80.679c-3.149-4.217-7.429-10.777-10.64-18.062-2.409-5.466-4.234-11.397-4.616-17.22-.451-6.887.506-12.435 2.468-16.773 2.438-5.386 6.31-8.898 11-10.805 4.375-1.78 9.317-2.044 14.299-1.054 5.972 1.188 12.128 4.212 17.377 8.551 4.393 3.63 11.565 9.52 18.424 15.127l6.061 4.952.284-53.02c-3.991 1.48-7.983 3.1-11.977 4.864a10.936 10.936 0 01-6.199 1.81 10.933 10.933 0 01-6.196-1.81c-29.266-12.933-58.454-18.06-87.631-17.874-29.92.189-70.303 5.989-100.736 14.704-1.105.375-2.288.58-3.522.58C4.888 310.614 0 305.726 0 299.698v-237.8c0-6.029 4.888-10.916 10.915-10.916h10.916v234.435c27.576-7.108 65.392-11.653 93.256-11.83 25.738-.162 51.474 3.419 77.24 12.158-16.027-12.993-33.898-22.509-52.946-28.723-24.01-7.835-60.141-10.438-86.723-8.156-6.005.492-11.273-3.974-11.765-9.979a10.608 10.608 0 01.003-1.861zm324.052 103.841c.295 7.593-.147 15.445-.548 22.562-.239 4.226-.46 8.143-.46 11.734a7.143 7.143 0 01-14.283 0c0-3.396.248-7.781.515-12.516 1-17.705 2.336-41.379-10.03-43.721h-11.158c.556 8.456.053 17.402-.399 25.417-.239 4.225-.46 8.142-.46 11.732a7.143 7.143 0 01-14.284 0c0-3.395.248-7.78.516-12.515.999-17.704 2.335-41.381-10.032-43.723h-9.369a7.195 7.195 0 01-1.57-.175l-.325 36.85a7.115 7.115 0 01-14.227-.111l.754-85.89-.022-.565c0-12.428-5.069-20.264-11.544-23.538-2.377-1.202-4.931-1.808-7.454-1.812a16.324 16.324 0 00-7.414 1.796c-6.412 3.259-11.413 11.127-11.413 23.849h-.027l-.76 142.342a7.115 7.115 0 01-14.228-.056l.07-13.121a7.122 7.122 0 01-1.193-.773 1777.083 1777.083 0 01-13.81-11.178 5011.309 5011.309 0 01-18.481-15.182c-3.448-2.851-7.351-4.809-11.016-5.539-2.356-.468-4.517-.416-6.209.272-1.379.561-2.556 1.675-3.355 3.442-1.043 2.306-1.532 5.593-1.241 10.022.257 3.927 1.625 8.238 3.444 12.366 2.692 6.107 6.344 11.688 9.038 15.293.159.214.305.434.437.659l54.239 82.073a7.084 7.084 0 011.135 3.097c1.085 8.936 2.982 15.62 5.781 19.813 2.03 3.043 4.659 4.593 7.941 4.509l.167-.002v-.026h84.431l.591.025c5.292-.08 10.128-1.649 14.509-4.679 4.843-3.348 9.266-8.543 13.271-15.547l1.027-1.747c10.541-18.17 20.77-35.804 21.844-56.749l-.543-24.29a7.204 7.204 0 01-.077-1.046l.073-5.697c.203-17.772.514-44.991-14.267-47.651h-9.372l-.212-.004zm-74.486-109.385c20.719-5.113 46.06-7.293 65.22-5.771V24.253c-48.14-4.545-107.824 5.005-135.596 49.25v191.441a343.885 343.885 0 017.347-4.774l.053-9.929h-.028c0-18.89 8.423-31.079 19.224-36.571a30.51 30.51 0 0113.886-3.302 30.82 30.82 0 0113.871 3.319c6.73 3.403 12.554 9.359 16.023 17.795zm57.363 183.293a5.833 5.833 0 0111.665 0v26.677a5.833 5.833 0 01-11.665 0v-26.677zm-34.477-11.786a5.833 5.833 0 0111.665 0v38.461a5.834 5.834 0 01-11.665 0v-38.461zM198.259 262.846V73.178C172.635 28.033 111.358 16.964 63.924 23.74l-1.225 202.662c25.265-.9 60.166 2.315 83.419 9.901 18.519 6.04 36.056 14.847 52.141 26.543z"/>
                            </svg>
                            </div>
                            <div class="map-category-content">
                                <div class="map-category-title">Reading & Cognitive Support</div>
                                <div id="map-category-text-desc" class="map-category-desc">Tools to improve focus, clarity, and cognitive ease.</div>
                            </div>
                            <div class="map-category-arrow" aria-hidden="true">
                                <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                                    <path d="M8.59 16.59L13.17 12 8.59 7.41 10 6l6 6-6 6-1.41-1.41z"/>
                                </svg>
                            </div>
                        </button>
                        <!-- text & typography Category -->
                        <button id="map-category-navigation" class="map-category-button" type="button" role="menuitem" data-category="navigation" aria-describedby="map-category-navigation-desc">
                            <div class="map-category-icon map-category-icon-navigation">
                                <!--icon -->
                                <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                    <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"/>
                                    <polyline points="14,2 14,8 20,8"/>
                                    <line x1="16" y1="13" x2="8" y2="13"/>
                                    <line x1="16" y1="17" x2="8" y2="17"/>
                                    <polyline points="10,9 9,9 8,9"/>
                                </svg>

                            </div>
                            <div class="map-category-content">
                                <div class="map-category-title">Text & Typography</div>
                                <div id="map-category-navigation-desc" class="map-category-desc">Tweak text style to enhance readability and accessibility.</div>
                            </div>
                            <div class="map-category-arrow" aria-hidden="true">
                                <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                                    <path d="M8.59 16.59L13.17 12 8.59 7.41 10 6l6 6-6 6-1.41-1.41z"/>
                                </svg>
                            </div>
                        </button>
                        <!-- visual adjustment Category -->
                        <button id="map-category-colors" class="map-category-button" type="button" role="menuitem" data-category="colors" aria-describedby="map-category-colors-desc">
                            <div class="map-category-icon map-category-icon-colors">
                                <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                    <circle cx="13.5" cy="6.5" r=".5"/>
                                    <circle cx="17.5" cy="10.5" r=".5"/>
                                    <circle cx="8.5" cy="7.5" r=".5"/>
                                    <circle cx="6.5" cy="12.5" r=".5"/>
                                    <path d="M12 2C6.5 2 2 6.5 2 12s4.5 10 10 10c.926 0 1.648-.746 1.648-1.688 0-.437-.18-.835-.437-1.125-.29-.289-.438-.652-.438-1.125a1.64 1.64 0 0 1 1.668-1.668h1.996c3.051 0 5.555-2.503 5.555-5.554C21.965 6.012 17.461 2 12 2z"/>
                                </svg>
                            </div>
                            <div class="map-category-content">
                                <div class="map-category-title">Visual Adjustments</div>
                                <div id="map-category-colors-desc" class="map-category-desc">Change colors, contrast, and themes to reduce eye strain.</div>
                            </div>
                            <div class="map-category-arrow" aria-hidden="true">
                                <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                                    <path d="M8.59 16.59L13.17 12 8.59 7.41 10 6l6 6-6 6-1.41-1.41z"/>
                                </svg>
                            </div>
                        </button>
                        <!-- settings Category -->
                        <button id="map-category-preferences" class="map-category-button" type="button" role="menuitem" data-category="preferences" aria-describedby="map-category-preferences-desc">
                            <div class="map-category-icon map-category-icon-preferences">
                                <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                    <circle cx="12" cy="12" r="3"/>
                                    <path d="M19.4 15a1.65 1.65 0 0 0 .33 1.82l.06.06a2 2 0 0 1 0 2.83 2 2 0 0 1-2.83 0l-.06-.06a1.65 1.65 0 0 0-1.82-.33 1.65 1.65 0 0 0-1 1.51V21a2 2 0 0 1-2 2 2 2 0 0 1-2-2v-.09A1.65 1.65 0 0 0 9 19.4a1.65 1.65 0 0 0-1.82.33l-.06.06a2 2 0 0 1-2.83 0 2 2 0 0 1 0-2.83l.06-.06a1.65 1.65 0 0 0 .33-1.82 1.65 1.65 0 0 0-1.51-1H3a2 2 0 0 1-2-2 2 2 0 0 1 2-2h.09A1.65 1.65 0 0 0 4.6 9a1.65 1.65 0 0 0-.33-1.82l-.06-.06a2 2 0 0 1 0-2.83 2 2 0 0 1 2.83 0l.06.06a1.65 1.65 0 0 0 1.82.33H9a1.65 1.65 0 0 0 1-1.51V3a2 2 0 0 1 2-2 2 2 0 0 1 2 2v.09a1.65 1.65 0 0 0 1 1.51 1.65 1.65 0 0 0 1.82-.33l.06-.06a2 2 0 0 1 2.83 0 2 2 0 0 1 0 2.83l-.06.06a1.65 1.65 0 0 0-.33 1.82V9a1.65 1.65 0 0 0 1.51 1H21a2 2 0 0 1 2 2 2 2 0 0 1-2 2h-.09a1.65 1.65 0 0 0-1.51 1z"/>
                                </svg>
                            </div>
                            <div class="map-category-content">
                                <div class="map-category-title">Settings</div>
                                <div id="map-category-preferences-desc" class="map-category-desc">Manage language, layout, and menu behavior.</div>
                            </div>
                            <div class="map-category-arrow" aria-hidden="true">
                                <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                                    <path d="M8.59 16.59L13.17 12 8.59 7.41 10 6l6 6-6 6-1.41-1.41z"/>
                                </svg>
                            </div>
                        </button>
                    </div>
                </div>

                <!-- Text Category View (Level 2) -->
                <div id="map-view-text" class="map-modal-view" role="region" aria-label="Text accessibility options" data-title="Reading & Cognitive Support">
                    <div class="map-view-content">
                        <!-- Text-to-Speech Section -->
                        <div class="map-feature-section">
                            <button id="map-tts-toggle" class="map-feature-toggle" data-active="false" type="button">
                                <div class="map-feature-icon">
                                    <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
                                        <path d="M3 9v6h4l5 5V4L7 9H3zm13.5 3c0-1.77-1.02-3.29-2.5-4.03v8.05c1.48-.73 2.5-2.25 2.5-4.02zM14 3.23v2.06c2.89.86 5 3.54 5 6.71s-2.11 5.85-5 6.71v2.06c4.01-.91 7-4.49 7-8.77s-2.99-7.86-7-8.77z"/>
                                    </svg>
                                </div>
                                <div class="map-feature-content">
                                    <div class="map-feature-title">Text-to-Speech</div>
                                    <div class="map-feature-desc">Select text to hear it read aloud</div>
                                </div>
                                <div class="map-toggle-switch">
                                    <span class="map-toggle-slider"></span>
                                </div>
                            </button>
                        </div>

                        <!-- Dyslexic Font Toggle Section -->
                        <div class="map-feature-section">
                            <button id="map-dyslexic-font-toggle" class="map-feature-toggle" data-active="false" type="button">
                                <div class="map-feature-icon">
                                    <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
                                        <!-- Clean "D" letter only -->
                                        <path d="M4 3h8c5.5 0 10 4.5 10 10s-4.5 10-10 10H4V3zm3 3v14h5c3.9 0 7-3.1 7-7s-3.1-7-7-7H7z"/>
                                    </svg>
                                </div>
                                <div class="map-feature-content">
                                    <div class="map-feature-title">Dyslexic Font</div>
                                    <div class="map-feature-desc">Apply dyslexia-friendly font to improve readability</div>
                                </div>
                                <div class="map-toggle-switch">
                                    <span class="map-toggle-slider"></span>
                                </div>
                            </button>
                        </div>

                        <!-- Reading Guide Section -->
                        <div class="map-feature-section">
                            <button id="map-reading-guide-toggle" class="map-feature-toggle" data-active="false" type="button">
                                <div class="map-feature-icon">
                                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                        <g transform="rotate(30 12 12)">
                                            <!-- Main ruler body -->
                                            <rect x="2" y="8" width="20" height="8" fill="none" stroke="currentColor" stroke-width="2"/>

                                            <!-- Measurement marks along bottom edge -->
                                            <line x1="4" y1="16" x2="4" y2="14" stroke="currentColor" stroke-width="1.5"/>
                                            <line x1="5.5" y1="16" x2="5.5" y2="14.5" stroke="currentColor" stroke-width="1"/>
                                            <line x1="7" y1="16" x2="7" y2="14" stroke="currentColor" stroke-width="1.5"/>
                                            <line x1="8.5" y1="16" x2="8.5" y2="14.5" stroke="currentColor" stroke-width="1"/>
                                            <line x1="10" y1="16" x2="10" y2="13.5" stroke="currentColor" stroke-width="2"/>
                                            <line x1="11.5" y1="16" x2="11.5" y2="14.5" stroke="currentColor" stroke-width="1"/>
                                            <line x1="13" y1="16" x2="13" y2="14" stroke="currentColor" stroke-width="1.5"/>
                                            <line x1="14.5" y1="16" x2="14.5" y2="14.5" stroke="currentColor" stroke-width="1"/>
                                            <line x1="16" y1="16" x2="16" y2="14" stroke="currentColor" stroke-width="1.5"/>
                                            <line x1="17.5" y1="16" x2="17.5" y2="14.5" stroke="currentColor" stroke-width="1"/>
                                            <line x1="19" y1="16" x2="19" y2="14" stroke="currentColor" stroke-width="1.5"/>
                                            <line x1="20.5" y1="16" x2="20.5" y2="14.5" stroke="currentColor" stroke-width="1"/>

                                            <!-- Circle detail in upper right -->
                                            <circle cx="18" cy="10.5" r="1.5" fill="none" stroke="currentColor" stroke-width="1.5"/>
                                            <circle cx="18" cy="10.5" r="0.5" fill="currentColor"/>
                                        </g>
                                    </svg>
                                </div>
                                <div class="map-feature-content">
                                    <div class="map-feature-title">Reading Guide</div>
                                    <div class="map-feature-desc">Show a horizontal line that follows your mouse to help focus on text</div>
                                </div>
                                <div class="map-toggle-switch">
                                    <span class="map-toggle-slider"></span>
                                </div>
                            </button>
                        </div>

                        <!-- ADHD Focus Mode Section -->
                        <div class="map-feature-section">
                            <button id="map-nav-adhd-focus-toggle" class="map-feature-toggle" data-active="false" type="button">
                                <div class="map-feature-icon">
                                    <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
                                        <!-- Outer focus ring - represents attention boundary -->
                                        <circle cx="12" cy="12" r="10" stroke="currentColor" stroke-width="1.5" fill="none" opacity="0.4"/>

                                        <!-- Inner focus circle - represents focused area -->
                                        <circle cx="12" cy="12" r="6" fill="currentColor" opacity="0.8"/>

                                        <!-- Central dot - represents point of focus -->
                                        <circle cx="12" cy="12" r="2" fill="currentColor"/>

                                        <!-- Focus rays - subtle directional indicators -->
                                        <path d="M12 2v4M12 18v4M2 12h4M18 12h4" stroke="currentColor" stroke-width="1.5" opacity="0.6"/>
                                    </svg>
                                </div>
                                <div class="map-feature-content">
                                    <div class="map-feature-title">ADHD Focus Mode</div>
                                    <div class="map-feature-desc">Dim areas outside reading zone to improve focus and concentration</div>
                                </div>
                                <div class="map-toggle-switch">
                                    <span class="map-toggle-slider"></span>
                                </div>
                            </button>
                        </div>

                        <!-- Big Cursor Section -->
                        <div class="map-feature-section">
                            <button id="map-big-cursor-toggle" class="map-feature-toggle" data-active="false" type="button">
                                <div class="map-feature-icon">
                                    <svg width="24" height="24" viewBox="0 0 100 100" xmlns="http://www.w3.org/2000/svg" version="1.1">
                                        <path fill="#ddd" d="M 5,5 90,30 65,50 95,80 80,95 50,65 30,90 z"/>
                                    </svg>
                                </div>
                                <div class="map-feature-content">
                                    <div class="map-feature-title">Big Cursor</div>
                                    <div class="map-feature-desc">Enlarge cursor size for better visibility and easier tracking</div>
                                </div>
                                <div class="map-toggle-switch">
                                    <span class="map-toggle-slider"></span>
                                </div>
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Colors & Contrast Category View (Level 2) -->
                <div id="map-view-colors" class="map-modal-view" role="region" aria-label="Color accessibility options" data-title="Visual Adjustments">
                    <div class="map-view-content">
                        <!-- Dark Mode Toggle -->
                        <div class="map-feature-section">
                            <button id="map-dark-mode-toggle" class="map-feature-toggle" data-active="false" type="button">
                                <div class="map-feature-icon">
      <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" aria-label="Dark mode icon" role="img">
  <!-- Moon shape -->
  <path d="M21 12.79A9 9 0 1 1 11.21 3 7 7 0 0 0 21 12.79z"/>
  
</svg>



                                </div>
                                <div class="map-feature-content">
                                    <div class="map-feature-title">Dark mode</div>
                                    <div class="map-feature-desc">Switch to a dark theme for the accessibility menu interface</div>
                                </div>
                                <div class="map-toggle-switch">
                                    <span class="map-toggle-slider"></span>
                                </div>
                            </button>
                        </div>

                        <!-- Visual Themes Section -->
                        <div class="map-feature-section">
                            <button id="map-contrast-themes-toggle" class="map-feature-toggle" data-active="false" type="button">
                                <div class="map-feature-icon">
                                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                    <rect x="3" y="3" width="18" height="18" rx="2" ry="2" fill="none" />
                                    <circle cx="8.5" cy="8.5" r="1.5" fill="none" />
                                    <path d="M21 15l-5-5L5 21" fill="none" />
                                    </svg>


                                </div>
                                <div class="map-feature-content">
                                    <div class="map-feature-title">Visual Themes</div>
                                    <div class="map-feature-desc">Choose a visual style that works best for you</div>
                                </div>
                                <div class="map-category-arrow">
                                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                        <path d="M9 18l6-6-6-6"/>
                                    </svg>
                                </div>
                            </button>

                            <div id="map-contrast-themes-content" class="map-feature-controls" style="display: none;">
                                <!-- Theme Selector Container -->
                                <div class="map-theme-selector">
                                    <!-- Theme Preview Card -->
                                    <div class="map-theme-preview-card">
                                        <!-- Navigation Arrows -->
                                        <button id="map-theme-prev" class="map-theme-nav map-theme-nav-prev" type="button" aria-label="Previous theme (applies immediately)">
                                            <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                                                <path d="M15.41 16.59L10.83 12l4.58-4.59L14 6l-6 6 6 6 1.41-1.41z"/>
                                            </svg>
                                        </button>

                                        <button id="map-theme-next" class="map-theme-nav map-theme-nav-next" type="button" aria-label="Next theme (applies immediately)">
                                            <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                                                <path d="M8.59 16.59L13.17 12 8.59 7.41 10 6l6 6-6 6-1.41-1.41z"/>
                                            </svg>
                                        </button>

                                        <!-- Theme Icon Preview -->
                                        <div class="map-theme-icon-preview-container">
                                            <div id="map-theme-icon-preview" class="map-theme-icon-preview" data-theme="normal">
                                                <svg width="48" height="48" viewBox="0 0 24 24" fill="currentColor">
                                                    <path d="M10 20v-6h4v6h5v-8h3L12 3 2 12h3v8z"/>
                                                </svg>
                                            </div>
                                        </div>

                                        <!-- Theme Info -->
                                        <div class="map-theme-info">
                                            <h4 id="map-theme-name" class="map-theme-title">Default</h4>
                                        </div>
                                    </div>

                                    <!-- Theme Dots Indicator -->
                                    <div class="map-theme-dots">
                                        <button class="map-theme-dot active" data-theme="normal" aria-label="Apply default theme"></button>
                                        <button class="map-theme-dot" data-theme="monochrome" aria-label="Monochrome theme"></button>
                                        <button class="map-theme-dot" data-theme="low-saturation" aria-label="Low saturation theme"></button>
                                        <button class="map-theme-dot" data-theme="high-saturation" aria-label="High saturation theme"></button>
                                        <button class="map-theme-dot" data-theme="dark" aria-label="Dark theme"></button>
                                        <button class="map-theme-dot" data-theme="high-contrast" aria-label="High contrast theme"></button>
                                        <button class="map-theme-dot" data-theme="sepia" aria-label="Sepia theme"></button>
                                        <button class="map-theme-dot" data-theme="colorblind" aria-label="Color blind friendly theme"></button>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Color Studio Section -->
                        <div class="map-feature-section">
                            <button id="map-custom-theme-toggle" class="map-feature-toggle" data-active="false" type="button">
                                <div class="map-feature-icon">
                                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 444.892 444.892" width="24" height="24" fill="currentColor">
                                    <path d="M440.498 173.103c5.858-5.857 5.858-15.355 0-21.213l-22.511-22.511c-5.091-5.091-13.084-5.846-19.038-1.8l-47.332 32.17 31.975-47.652c3.993-5.951 3.219-13.897-1.85-18.964l-48.83-48.83c-4.508-4.508-11.372-5.675-17.114-2.908l-8.443 4.065 4.043-8.97c2.563-5.685 1.341-12.361-3.068-16.771L293.002 4.393c-5.857-5.857-15.355-5.857-21.213 0l-119.06 119.059 168.71 168.71L440.498 173.103z"/>
                                    <path d="M130.56 145.622l-34.466 34.466c-2.813 2.813-4.394 6.628-4.394 10.606s1.58 7.794 4.394 10.606l32.694 32.694c6.299 6.299 9.354 14.992 8.382 23.849-0.971 8.851-5.843 16.677-13.366 21.473-93.28 93.281-102.236 102.236-105.178 105.179-21.119 21.118-21.119 55.48 0 76.6 21.14 21.14 55.504 21.098 76.6 0 2.944-2.943 11.902-11.902 73.136-107.965 4.784-7.505 12.607-12.366 21.462-13.339 8.883-0.969 17.575 2.071 23.859 8.354l32.694 32.694c5.857 5.857 15.356 5.857 21.213 0l34.467-34.467-169.655-169.655zM70.05 404.825c-8.28 8.28-21.704 8.28-29.983 0-8.28-8.28-8.28-21.704 0-29.983 8.28-8.28 21.704-8.28 29.983 0 8.28 8.28 8.28 21.704 0 29.983z"/>
                                    </svg>
                                </div>
                                <div class="map-feature-content">
                                    <div class="map-feature-title">Color Studio</div>
                                    <div class="map-feature-desc">Design your perfect color palette with live preview</div>
                                </div>
                                <div class="map-category-arrow">
                                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                        <path d="M9 18l6-6-6-6"/>
                                    </svg>
                                </div>
                            </button>

                            <div id="map-custom-theme-content" class="map-feature-controls" style="display: none;">
                                <!-- Premium Compact Color Studio Layout -->
                                <div class="map-color-studio-compact">
                                    <!-- Text Color Row -->
                                    <div class="map-color-row">
                                        <div class="map-color-label">
                                            <div class="map-color-icon">
                                                <svg width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                                    <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"/>
                                                    <polyline points="14,2 14,8 20,8"/>
                                                    <line x1="16" y1="13" x2="8" y2="13"/>
                                                    <line x1="16" y1="17" x2="8" y2="17"/>
                                                    <polyline points="10,9 9,9 8,9"/>
                                                </svg>
                                            </div>
                                            <div class="map-color-info">
                                                <span class="map-color-title">Text</span>
                                                <span class="map-color-desc">Body text & paragraphs</span>
                                            </div>
                                        </div>
                                        <div class="map-color-controls">
                                            <div class="map-color-picker-wrapper">
                                                <input type="color" id="map-custom-text-color" class="map-color-picker-compact" value="">
                                                <button type="button" class="map-color-reset-btn" data-target="map-custom-text-color" aria-label="Reset text color" style="display: none;">
                                                    <svg width="10" height="10" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="3" stroke-linecap="round" stroke-linejoin="round">
                                                        <path d="M18 6L6 18"/>
                                                        <path d="M6 6l12 12"/>
                                                    </svg>
                                                </button>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Background Color Row -->
                                    <div class="map-color-row">
                                        <div class="map-color-label">
                                            <div class="map-color-icon">
                                                <svg width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                                    <rect x="3" y="3" width="18" height="18" rx="2" ry="2"/>
                                                    <circle cx="8.5" cy="8.5" r="1.5"/>
                                                    <polyline points="21,15 16,10 5,21"/>
                                                </svg>
                                            </div>
                                            <div class="map-color-info">
                                                <span class="map-color-title">Background</span>
                                                <span class="map-color-desc">Page & content areas</span>
                                            </div>
                                        </div>
                                        <div class="map-color-controls">
                                            <div class="map-color-picker-wrapper">
                                                <input type="color" id="map-custom-bg-color" class="map-color-picker-compact" value="">
                                                <button type="button" class="map-color-reset-btn" data-target="map-custom-bg-color" aria-label="Reset background color" style="display: none;">
                                                    <svg width="10" height="10" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="3" stroke-linecap="round" stroke-linejoin="round">
                                                        <path d="M18 6L6 18"/>
                                                        <path d="M6 6l12 12"/>
                                                    </svg>
                                                </button>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Links Color Row -->
                                    <div class="map-color-row">
                                        <div class="map-color-label">
                                            <div class="map-color-icon">
                                                <svg width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                                    <path d="M10 13a5 5 0 0 0 7.54.54l3-3a5 5 0 0 0-7.07-7.07l-1.72 1.71"/>
                                                    <path d="M14 11a5 5 0 0 0-7.54-.54l-3 3a5 5 0 0 0 7.07 7.07l1.71-1.71"/>
                                                </svg>
                                            </div>
                                            <div class="map-color-info">
                                                <span class="map-color-title">Links</span>
                                                <span class="map-color-desc">Hyperlinks & buttons</span>
                                            </div>
                                        </div>
                                        <div class="map-color-controls">
                                            <div class="map-color-picker-wrapper">
                                                <input type="color" id="map-custom-link-color" class="map-color-picker-compact" value="">
                                                <button type="button" class="map-color-reset-btn" data-target="map-custom-link-color" aria-label="Reset link color" style="display: none;">
                                                    <svg width="10" height="10" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="3" stroke-linecap="round" stroke-linejoin="round">
                                                        <path d="M18 6L6 18"/>
                                                        <path d="M6 6l12 12"/>
                                                    </svg>
                                                </button>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Headings Color Row -->
                                    <div class="map-color-row">
                                        <div class="map-color-label">
                                            <div class="map-color-icon">
                                                <svg width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                                    <path d="M6 12h12"/>
                                                    <path d="M6 20V4"/>
                                                    <path d="M18 20V4"/>
                                                </svg>
                                            </div>
                                            <div class="map-color-info">
                                                <span class="map-color-title">Headings</span>
                                                <span class="map-color-desc">Titles & headers</span>
                                            </div>
                                        </div>
                                        <div class="map-color-controls">
                                            <div class="map-color-picker-wrapper">
                                                <input type="color" id="map-custom-heading-color" class="map-color-picker-compact" value="">
                                                <button type="button" class="map-color-reset-btn" data-target="map-custom-heading-color" aria-label="Reset heading color" style="display: none;">
                                                    <svg width="10" height="10" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="3" stroke-linecap="round" stroke-linejoin="round">
                                                        <path d="M18 6L6 18"/>
                                                        <path d="M6 6l12 12"/>
                                                    </svg>
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- text & typography Category View (Level 2) -->
                <div id="map-view-navigation" class="map-modal-view" role="region" aria-label="Navigation accessibility options" data-title="Text & Typography">
                    <div class="map-view-content">


                        <!-- Text Magnification Section -->
                        <div class="map-feature-section">
                            <button id="map-text-magnification-toggle" class="map-feature-toggle" data-active="false" type="button">
                                <div class="map-feature-icon">
                                    <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                        <!-- Magnifying glass circle -->
                                        <circle cx="11" cy="11" r="8" fill="currentColor"/>
                                        <!-- Magnifying glass handle -->
                                        <path d="m21 21-4.35-4.35" fill="none" stroke="currentColor"/>
                                    </svg>
                                </div>
                                <div class="map-feature-content">
                                    <div class="map-feature-title">Text Magnification</div>
                                    <div class="map-feature-desc">Magnify text on hover for better readability and visibility</div>
                                </div>
                                <div class="map-toggle-switch">
                                    <span class="map-toggle-slider"></span>
                                </div>
                            </button>
                        </div>

                        <!-- Font Size Control Section -->
                        <div class="map-feature-section">
                            <button id="map-font-size-toggle" class="map-feature-toggle" data-active="false" type="button">
                                <div class="map-feature-icon">
                                    <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
                                        <!-- Large T letter (main) -->
                                        <path d="M8 2h14v4h-5v16h-4V6H8V2z"/>

                                        <!-- Small T letter (positioned lower left) -->
                                        <path d="M2 12h8v2.5H7v7.5H5v-7.5H2V12z"/>
                                    </svg>
                                </div>
                                <div class="map-feature-content">
                                    <div class="map-feature-title">Font Size</div>
                                    <div class="map-feature-desc">Adjust text size for better readability</div>
                                </div>
                                <div class="map-feature-status">
                                    <span id="map-font-size-value">Default</span>
                                </div>
                                <div class="map-category-arrow">
                                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                        <path d="M9 18l6-6-6-6"/>
                                    </svg>
                                </div>
                            </button>

                            <div id="map-font-size-controls" class="map-feature-controls" style="display: none;">
                                <div class="map-font-size-controls">
                                    <div class="map-size-control-group">
                                        <button id="map-font-size-decrease" class="map-size-control-btn map-size-decrease" type="button" aria-label="Decrease font size">
                                            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2.5" stroke-linecap="round">
                                                <path d="M5 12h14"/>
                                            </svg>
                                        </button>

                                        <div class="map-size-indicator">
                                            <div class="map-size-preview">Aa</div>
                                            <div class="map-size-percentage" id="map-font-percentage">100%</div>
                                        </div>

                                        <button id="map-font-size-increase" class="map-size-control-btn map-size-increase" type="button" aria-label="Increase font size">
                                            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2.5" stroke-linecap="round">
                                                <path d="M12 5v14M5 12h14"/>
                                            </svg>
                                        </button>
                                    </div>

                                    <button id="map-font-size-reset" class="map-control-reset" type="button" aria-label="Reset font size to default">
                                        <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                            <path d="M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8"/>
                                            <path d="M21 3v5h-5"/>
                                            <path d="M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16"/>
                                            <path d="M3 21v-5h5"/>
                                        </svg>
                                        Reset to Default
                                    </button>
                                </div>
                            </div>
                        </div>

                        <!-- Line Spacing Control Section -->
                        <div class="map-feature-section">
                            <button id="map-line-spacing-toggle" class="map-feature-toggle" data-active="false" type="button">
                                <div class="map-feature-icon">
                                    <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
                                        <path d="M6 7h12v2H6V7zm0 4h12v2H6v-2zm0 4h12v2H6v-2z"/>
                                        <path d="M3 4h2v16H3V4z"/>
                                        <path d="M19 4h2v16h-2V4z"/>
                                        <circle cx="4" cy="6" r="1" fill="currentColor"/>
                                        <circle cx="4" cy="12" r="1" fill="currentColor"/>
                                        <circle cx="4" cy="18" r="1" fill="currentColor"/>
                                        <circle cx="20" cy="6" r="1" fill="currentColor"/>
                                        <circle cx="20" cy="12" r="1" fill="currentColor"/>
                                        <circle cx="20" cy="18" r="1" fill="currentColor"/>
                                    </svg>
                                </div>
                                <div class="map-feature-content">
                                    <div class="map-feature-title">Line Spacing</div>
                                    <div class="map-feature-desc">Adjust space between lines for better readability</div>
                                </div>
                                <div class="map-feature-status">
                                    <span id="map-line-spacing-value">Default</span>
                                </div>
                                <div class="map-category-arrow">
                                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                        <path d="M9 18l6-6-6-6"/>
                                    </svg>
                                </div>
                            </button>

                            <div id="map-line-spacing-controls" class="map-feature-controls" style="display: none;">
                                <div class="map-line-spacing-controls">
                                    <div class="map-spacing-control-group">
                                        <div class="map-spacing-labels">
                                            <span class="map-spacing-label-min">Tight</span>
                                            <span class="map-spacing-label-center">Normal</span>
                                            <span class="map-spacing-label-max">Wide</span>
                                        </div>

                                        <div class="map-slider-container">
                                            <input type="range"
                                                   id="map-line-spacing-slider"
                                                   class="map-premium-slider"
                                                   min="1.0"
                                                   max="2.5"
                                                   step="0.1"
                                                   value="1.5"
                                                   aria-label="Adjust line spacing">
                                            <div class="map-slider-track">
                                                <div class="map-slider-progress" id="map-slider-progress"></div>
                                            </div>
                                        </div>

                                        <div class="map-spacing-value">
                                            <span id="map-spacing-numeric">1.5x</span>
                                        </div>
                                    </div>

                                    <button id="map-line-spacing-reset" class="map-control-reset" type="button" aria-label="Reset line spacing to default">
                                        <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                            <path d="M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8"/>
                                            <path d="M21 3v5h-5"/>
                                            <path d="M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16"/>
                                            <path d="M3 21v-5h5"/>
                                        </svg>
                                        Reset to Default
                                    </button>
                                </div>
                            </div>
                        </div>
                        <!-- Letter Spacing Control Section -->
                        <div class="map-feature-section">
                            <button id="map-letter-spacing-toggle" class="map-feature-toggle" data-active="false" type="button">
                                <div class="map-feature-icon">
<svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor" xmlns="http://www.w3.org/2000/svg">
  <!-- Letter Y -->
  <text x="2" y="15" font-family="Arial, sans-serif" font-weight="bold" font-size="12">Y</text>
  <!-- Letter Z -->
  <text x="16.5" y="15" font-family="Arial, sans-serif" font-weight="bold" font-size="12">Z</text>
  <!-- - symbole indicating spacing between letters -->
   <path d="M10 12h4" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
</svg>








                                </div>
                                <div class="map-feature-content">
                                    <div class="map-feature-title">Letter Spacing</div>
                                    <div class="map-feature-desc">Adjust space between letters for better readability</div>
                                </div>
                                <div class="map-feature-status">
                                    <span id="map-letter-spacing-value">Default</span>
                                </div>
                                <div class="map-category-arrow">
                                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                        <path d="M9 18l6-6-6-6"/>
                                    </svg>
                                </div>
                            </button>

                            <div id="map-letter-spacing-controls" class="map-feature-controls" style="display: none;">
                                <div class="map-line-spacing-controls">
                                    <div class="map-spacing-control-group">
                                        <div class="map-spacing-labels">
                                            <span class="map-spacing-label-min">Tight</span>
                                            <span class="map-spacing-label-center">Normal</span>
                                            <span class="map-spacing-label-max">Wide</span>
                                        </div>

                                        <div class="map-slider-container">
                                            <input type="range"
                                                   id="map-letter-spacing-slider"
                                                   class="map-premium-slider"
                                                   min="0"
                                                   max="0.2"
                                                   step="0.01"
                                                   value="0"
                                                   aria-label="Adjust letter spacing">
                                            <div class="map-slider-track">
                                                <div class="map-slider-progress" id="map-letter-slider-progress"></div>
                                            </div>
                                        </div>

                                        <div class="map-spacing-value">
                                            <span id="map-letter-spacing-numeric">0em</span>
                                        </div>
                                    </div>

                                    <button id="map-letter-spacing-reset" class="map-control-reset" type="button" aria-label="Reset letter spacing to default">
                                        <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                            <path d="M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8"/>
                                            <path d="M21 3v5h-5"/>
                                            <path d="M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16"/>
                                            <path d="M3 21v-5h5"/>
                                        </svg>
                                        Reset to Default
                                    </button>
                                </div>
                            </div>
                        </div>

                        <!-- Text Alignment Control Section -->
                        <div class="map-feature-section">
                            <button id="map-text-alignment-toggle" class="map-feature-toggle" data-active="false" type="button">
                                <div class="map-feature-icon">
                                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                        <!-- Text alignment lines -->
                                        <line x1="3" y1="6" x2="21" y2="6"/>
                                        <line x1="3" y1="12" x2="15" y2="12"/>
                                        <line x1="3" y1="18" x2="21" y2="18"/>
                                    </svg>
                                </div>
                                <div class="map-feature-content">
                                    <div class="map-feature-title">Text Alignment</div>
                                    <div class="map-feature-desc">Change text alignment for better reading experience</div>
                                </div>
                                <div class="map-feature-status">
                                    <span id="map-text-alignment-value">Default</span>
                                </div>
                                <div class="map-category-arrow">
                                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                        <path d="M9 18l6-6-6-6"/>
                                    </svg>
                                </div>
                            </button>

                            <div id="map-text-alignment-controls" class="map-feature-controls" style="display: none;">
                                <div class="map-alignment-controls">
                                    <div class="map-alignment-grid">
                                        <!-- Top Row -->
                                        <button id="map-align-left" class="map-alignment-btn" data-alignment="left" type="button" aria-label="Align text to left">
                                            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                                <line x1="3" y1="6" x2="21" y2="6"/>
                                                <line x1="3" y1="12" x2="15" y2="12"/>
                                                <line x1="3" y1="18" x2="21" y2="18"/>
                                            </svg>
                                        </button>

                                        <button id="map-align-justify" class="map-alignment-btn" data-alignment="justify" type="button" aria-label="Justify text">
                                            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                                <line x1="3" y1="6" x2="21" y2="6"/>
                                                <line x1="3" y1="12" x2="21" y2="12"/>
                                                <line x1="3" y1="18" x2="21" y2="18"/>
                                            </svg>
                                        </button>

                                        <!-- Bottom Row -->
                                        <button id="map-align-center" class="map-alignment-btn" data-alignment="center" type="button" aria-label="Center text">
                                            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                                <line x1="8" y1="6" x2="16" y2="6"/>
                                                <line x1="6" y1="12" x2="18" y2="12"/>
                                                <line x1="8" y1="18" x2="16" y2="18"/>
                                            </svg>
                                        </button>

                                        <button id="map-align-right" class="map-alignment-btn" data-alignment="right" type="button" aria-label="Align text to right">
                                            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                                <line x1="3" y1="6" x2="21" y2="6"/>
                                                <line x1="9" y1="12" x2="21" y2="12"/>
                                                <line x1="3" y1="18" x2="21" y2="18"/>
                                            </svg>
                                        </button>
                                    </div>

                                    <button id="map-text-alignment-reset" class="map-control-reset" type="button" aria-label="Reset text alignment to default">
                                        <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                            <path d="M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8"/>
                                            <path d="M21 3v5h-5"/>
                                            <path d="M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16"/>
                                            <path d="M3 21v-5h5"/>
                                        </svg>
                                        Reset to Default
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Preferences Category View (Level 2) -->
                <div id="map-view-preferences" class="map-modal-view" role="region" aria-label="General preferences" data-title="Settings">
                    <div class="map-view-content">


                        <!-- Menu Position Selection -->
                        <div class="map-feature-section">
                            <button id="map-menu-position-toggle" class="map-feature-toggle" data-active="false" type="button">
                                <div class="map-feature-icon">
                                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                        <!-- Position/Move icon -->
                                        <path d="M5 9l-3 3 3 3M9 5l3-3 3 3M15 19l-3 3-3-3M19 9l3 3-3 3"/>
                                        <line x1="2" y1="12" x2="22" y2="12"/>
                                        <line x1="12" y1="2" x2="12" y2="22"/>
                                    </svg>
                                </div>
                                <div class="map-feature-content">
                                    <div class="map-feature-title">Button Position</div>
                                    <div class="map-feature-desc">Choose where the accessibility menu button appears on screen</div>
                                </div>
                                <div class="map-category-arrow">
                                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                        <path d="M9 18l6-6-6-6"/>
                                    </svg>
                                </div>
                            </button>

                            <div id="map-menu-position-content" class="map-feature-controls" style="display: none;">
                                <!-- Position Options using existing language option classes -->
                                <div class="map-language-options">
                                    <div class="map-position-grid">
                                        <!-- Top Row -->
                                        <div class="map-language-option" data-position="top-left">
                                            <div class="map-language-info">
                                                <div class="map-language-flag">↖</div>
                                                <div class="map-language-details">
                                                    <span class="map-language-name">Top Left</span>
                                                </div>
                                            </div>
                                            <div class="map-language-status">
                                                <div class="map-language-check">
                                                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                                        <polyline points="20,6 9,17 4,12"/>
                                                    </svg>
                                                </div>
                                            </div>
                                        </div>

                                        <div class="map-language-option" data-position="top-right">
                                            <div class="map-language-info">
                                                <div class="map-language-flag">↗</div>
                                                <div class="map-language-details">
                                                    <span class="map-language-name">Top Right</span>
                                                </div>
                                            </div>
                                            <div class="map-language-status">
                                                <div class="map-language-check">
                                                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                                        <polyline points="20,6 9,17 4,12"/>
                                                    </svg>
                                                </div>
                                            </div>
                                        </div>

                                        <!-- Bottom Row -->
                                        <div class="map-language-option" data-position="bottom-left">
                                            <div class="map-language-info">
                                                <div class="map-language-flag">↙</div>
                                                <div class="map-language-details">
                                                    <span class="map-language-name">Bottom Left</span>
                                                </div>
                                            </div>
                                            <div class="map-language-status">
                                                <div class="map-language-check">
                                                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                                        <polyline points="20,6 9,17 4,12"/>
                                                    </svg>
                                                </div>
                                            </div>
                                        </div>

                                        <div class="map-language-option active" data-position="bottom-right">
                                            <div class="map-language-info">
                                                <div class="map-language-flag">↘</div>
                                                <div class="map-language-details">
                                                    <span class="map-language-name">Bottom Right</span>
                                                </div>
                                            </div>
                                            <div class="map-language-status">
                                                <div class="map-language-check">
                                                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                                        <polyline points="20,6 9,17 4,12"/>
                                                    </svg>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Language Selection -->
                        <div class="map-feature-section">
                            <button id="map-language-toggle" class="map-feature-toggle" data-active="false" type="button">
                                <div class="map-feature-icon">
                                    <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
                                        <!-- Translation/Language icon -->
                                        <path d="M12.87 15.07l-2.54-2.51.03-.03A17.52 17.52 0 0014.07 6H17V4h-7V2H8v2H1v2h11.17C11.5 7.92 10.44 9.75 9 11.35 8.07 10.32 7.3 9.19 6.69 8h-2c.73 1.63 1.73 3.17 2.98 4.56l-5.09 5.02L4 19l5-5 3.11 3.11.76-2.04zM18.5 10h-2L12 22h2l1.12-3h4.75L21 22h2l-4.5-12zm-2.62 7l1.62-4.33L19.12 17h-3.24z"/>
                                    </svg>
                                </div>
                                <div class="map-feature-content">
                                    <div class="map-feature-title">Language</div>
                                    <div class="map-feature-desc">Choose your preferred interface language</div>
                                </div>
                                <div class="map-category-arrow">
                                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                        <path d="M9 18l6-6-6-6"/>
                                    </svg>
                                </div>
                            </button>

                            <div id="map-language-content" class="map-feature-controls" style="display: none;">
                                <!-- Language Options -->
                                <div class="map-language-options">
                                    <div class="map-language-option active" data-language="en">
                                        <div class="map-language-info">
                                            <div class="map-language-flag">
                                                <!-- US Flag SVG -->
                                                <svg width="20" height="15" viewBox="0 0 20 15" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                    <rect width="20" height="15" rx="2" fill="#B22234"/>
                                                    <rect width="20" height="1.15" fill="white"/>
                                                    <rect y="2.31" width="20" height="1.15" fill="white"/>
                                                    <rect y="4.62" width="20" height="1.15" fill="white"/>
                                                    <rect y="6.92" width="20" height="1.15" fill="white"/>
                                                    <rect y="9.23" width="20" height="1.15" fill="white"/>
                                                    <rect y="11.54" width="20" height="1.15" fill="white"/>
                                                    <rect y="13.85" width="20" height="1.15" fill="white"/>
                                                    <rect width="8" height="8" fill="#3C3B6E"/>
                                                </svg>
                                            </div>
                                            <div class="map-language-details">
                                                <span class="map-language-name">English</span>
                                                <span class="map-language-native">English</span>
                                            </div>
                                        </div>
                                        <div class="map-language-status">
                                            <div class="map-language-check">
                                                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                                    <polyline points="20,6 9,17 4,12"/>
                                                </svg>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="map-language-option" data-language="fr">
                                        <div class="map-language-info">
                                            <div class="map-language-flag">
                                                <!-- French Flag SVG -->
                                                <svg width="20" height="15" viewBox="0 0 20 15" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                    <rect width="20" height="15" rx="2" fill="#ED2939"/>
                                                    <rect width="6.67" height="15" fill="#002395"/>
                                                    <rect x="13.33" width="6.67" height="15" fill="#ED2939"/>
                                                    <rect x="6.67" width="6.67" height="15" fill="white"/>
                                                </svg>
                                            </div>
                                            <div class="map-language-details">
                                                <span class="map-language-name">French</span>
                                                <span class="map-language-native">Français</span>
                                            </div>
                                        </div>
                                        <div class="map-language-status">
                                            <div class="map-language-check">
                                                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                                    <polyline points="20,6 9,17 4,12"/>
                                                </svg>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="map-language-option" data-language="es">
                                        <div class="map-language-info">
                                            <div class="map-language-flag">
                                                <!-- Spanish Flag SVG -->
                                                <svg width="20" height="15" viewBox="0 0 20 15" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                    <rect width="20" height="15" rx="2" fill="#AA151B"/>
                                                    <rect y="3.75" width="20" height="7.5" fill="#F1BF00"/>
                                                </svg>
                                            </div>
                                            <div class="map-language-details">
                                                <span class="map-language-name">Spanish</span>
                                                <span class="map-language-native">Español</span>
                                            </div>
                                        </div>
                                        <div class="map-language-status">
                                            <div class="map-language-check">
                                                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                                    <polyline points="20,6 9,17 4,12"/>
                                                </svg>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="map-language-option" data-language="de">
                                        <div class="map-language-info">
                                            <div class="map-language-flag">
                                                <!-- German Flag SVG -->
                                                <svg width="20" height="15" viewBox="0 0 20 15" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                    <rect width="20" height="15" rx="2" fill="#FFCE00"/>
                                                    <rect width="20" height="5" fill="#000000"/>
                                                    <rect y="10" width="20" height="5" fill="#FFCE00"/>
                                                    <rect y="5" width="20" height="5" fill="#DD0000"/>
                                                </svg>
                                            </div>
                                            <div class="map-language-details">
                                                <span class="map-language-name">German</span>
                                                <span class="map-language-native">Deutsch</span>
                                            </div>
                                        </div>
                                        <div class="map-language-status">
                                            <div class="map-language-check">
                                                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                                    <polyline points="20,6 9,17 4,12"/>
                                                </svg>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="map-language-option" data-language="it">
                                        <div class="map-language-info">
                                            <div class="map-language-flag">
                                                <!-- Italian Flag SVG -->
                                                <svg width="20" height="15" viewBox="0 0 20 15" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                    <rect width="20" height="15" rx="2" fill="#CE2B37"/>
                                                    <rect width="6.67" height="15" fill="#009246"/>
                                                    <rect x="13.33" width="6.67" height="15" fill="#CE2B37"/>
                                                    <rect x="6.67" width="6.67" height="15" fill="white"/>
                                                </svg>
                                            </div>
                                            <div class="map-language-details">
                                                <span class="map-language-name">Italian</span>
                                                <span class="map-language-native">Italiano</span>
                                            </div>
                                        </div>
                                        <div class="map-language-status">
                                            <div class="map-language-check">
                                                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                                    <polyline points="20,6 9,17 4,12"/>
                                                </svg>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="map-language-option" data-language="pt">
                                        <div class="map-language-info">
                                            <div class="map-language-flag">
                                                <!-- Portuguese Flag SVG -->
                                                <svg width="20" height="15" viewBox="0 0 20 15" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                    <rect width="20" height="15" rx="2" fill="#FF0000"/>
                                                    <rect width="8" height="15" fill="#006600"/>
                                                    <circle cx="8" cy="7.5" r="3" fill="#FFFF00" stroke="#0000FF" stroke-width="0.5"/>
                                                </svg>
                                            </div>
                                            <div class="map-language-details">
                                                <span class="map-language-name">Portuguese</span>
                                                <span class="map-language-native">Português</span>
                                            </div>
                                        </div>
                                        <div class="map-language-status">
                                            <div class="map-language-check">
                                                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                                    <polyline points="20,6 9,17 4,12"/>
                                                </svg>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="map-language-option" data-language="ar">
                                        <div class="map-language-info">
                                            <div class="map-language-flag">
                                                <!-- Saudi Arabia Flag SVG -->
                                                <svg width="20" height="15" viewBox="0 0 20 15" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                    <rect width="20" height="15" rx="2" fill="#006C35"/>
                                                    <text x="10" y="9" font-family="Arial" font-size="6" fill="white" text-anchor="middle">العربية</text>
                                                </svg>
                                            </div>
                                            <div class="map-language-details">
                                                <span class="map-language-name">Arabic</span>
                                                <span class="map-language-native">العربية</span>
                                            </div>
                                        </div>
                                        <div class="map-language-status">
                                            <div class="map-language-check">
                                                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                                    <polyline points="20,6 9,17 4,12"/>
                                                </svg>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="map-language-option" data-language="zh">
                                        <div class="map-language-info">
                                            <div class="map-language-flag">
                                                <!-- Chinese Flag SVG -->
                                                <svg width="20" height="15" viewBox="0 0 20 15" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                    <rect width="20" height="15" rx="2" fill="#DE2910"/>
                                                    <polygon points="3,3 4,1 5,3 6,2 4,4" fill="#FFDE00"/>
                                                    <polygon points="7,2 7.5,1 8,2 8.5,1.5 7.5,2.5" fill="#FFDE00"/>
                                                    <polygon points="7,4 7.5,3 8,4 8.5,3.5 7.5,4.5" fill="#FFDE00"/>
                                                    <polygon points="7,6 7.5,5 8,6 8.5,5.5 7.5,6.5" fill="#FFDE00"/>
                                                    <polygon points="7,8 7.5,7 8,8 8.5,7.5 7.5,8.5" fill="#FFDE00"/>
                                                </svg>
                                            </div>
                                            <div class="map-language-details">
                                                <span class="map-language-name">Chinese</span>
                                                <span class="map-language-native">中文</span>
                                            </div>
                                        </div>
                                        <div class="map-language-status">
                                            <div class="map-language-check">
                                                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                                    <polyline points="20,6 9,17 4,12"/>
                                                </svg>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="map-language-option" data-language="ru">
                                        <div class="map-language-info">
                                            <div class="map-language-flag">
                                                <!-- Russian Flag SVG -->
                                                <svg width="20" height="15" viewBox="0 0 20 15" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                    <rect width="20" height="15" rx="2" fill="#0039A6"/>
                                                    <rect width="20" height="5" fill="white"/>
                                                    <rect y="10" width="20" height="5" fill="#D52B1E"/>
                                                    <rect y="5" width="20" height="5" fill="#0039A6"/>
                                                </svg>
                                            </div>
                                            <div class="map-language-details">
                                                <span class="map-language-name">Russian</span>
                                                <span class="map-language-native">Русский</span>
                                            </div>
                                        </div>
                                        <div class="map-language-status">
                                            <div class="map-language-check">
                                                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                                    <polyline points="20,6 9,17 4,12"/>
                                                </svg>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                </div> <!-- Close map-modal-views-container -->
            </div>
        </div>
    </div>

    <!-- jQuery (required by frontend.js) -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>

    <!-- Mock WordPress dependencies for standalone use -->
    <script>
        // Mock mapAjax object that would normally be provided by WordPress
        window.mapAjax = {
            plugin_url: './',
            settings: {
                speech_rate: '1.0',
                highlight_text: false,
                auto_scroll: false
            },
            strings: {
                play: 'Start Reading',
                pause: 'Pause',
                stop: 'Stop',
                error: 'An error occurred',
                no_text: 'No text found to read'
            }
        };

        // Translation files will be loaded directly from ./languages/ folder



    </script>

    <!-- Custom JavaScript -->
    <script src="landing-page.js"></script>

    <!-- Accessibility Plugin JavaScript - Modular System -->
    <!-- Core module must load first -->
    <script src="assets/js/modules/core.js"></script>

    <!-- Feature modules -->
    <script src="assets/js/modules/features/text-to-speech.js"></script>
    <script src="assets/js/modules/features/reading-cognitive.js"></script>
    <script src="assets/js/modules/features/visual-adjustments.js"></script>
    <script src="assets/js/modules/features/text-typography.js"></script>
    <script src="assets/js/modules/features/settings.js"></script>

    <!-- UI modules -->
    <script src="assets/js/modules/ui/modal-system.js"></script>

    <!-- Utility modules -->
    <script src="assets/js/modules/utils/keyboard.js"></script>
    <script src="assets/js/modules/utils/reset.js"></script>
    <script src="assets/js/modules/utils/storage.js"></script>
    <script src="assets/js/modules/utils/translations.js"></script>

    <!-- Initialization module must load last -->
    <script src="assets/js/modules/init.js"></script>
</body>
</html>
